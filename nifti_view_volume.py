import nibabel as nib
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches
import os

# Import des utilitaires pour les couleurs NDE
try:
    from utils.omniscan_div_cmap import get_omniscan_diverging_colormap
    from utils.utils_linear_scan_alongv import safe_division
except ImportError:
    print("⚠️ Modules utils non trouvés, utilisation des colormaps par défaut")
    def get_omniscan_diverging_colormap(path):
        return plt.cm.viridis
    def safe_division(num, den):
        return num / den if den != 0 else 0.0

class NIfTIVolumeViewer:
    def __init__(self, volume_path, colorize_mode='auto', omniscan_colormap_path='./OmniScanColorMap.npy'):
        # Chargement du volume
        self.volume_raw = nib.load(volume_path).get_fdata()
        self.volume_path = volume_path
        self.colorize_mode = colorize_mode  # 'auto', 'grayscale', 'rgb', 'classes'
        self.omniscan_colormap_path = omniscan_colormap_path
        self.current_slice = 0
        self.max_slice = self.volume_raw.shape[0] - 1

        print(f"Volume chargé : {self.volume_raw.shape} (Z, Y, X)")
        print(f"Type de données : {self.volume_raw.dtype}")
        print(f"Valeurs : min={self.volume_raw.min():.2f}, max={self.volume_raw.max():.2f}")

        # Analyser les données pour déterminer le type
        self.min_value = float(np.min(self.volume_raw))
        self.max_value = float(np.max(self.volume_raw))

        # Détection automatique du type de données
        unique_values = np.unique(self.volume_raw)
        print(f"Valeurs uniques dans le volume : {unique_values}")
        self.is_mask = self._detect_mask(unique_values)
        self.is_nde_data = self._detect_nde_data()

        if self.is_mask:
            type_text = "Masque de segmentation"
        elif self.is_nde_data:
            type_text = "Données NDE"
        else:
            type_text = "Volume d'intensité"
        print(f"📊 Type détecté : {type_text}")

        # Préparer les données selon le type détecté
        self._prepare_volume_data()

        # Configuration du colormap selon le type de données et le mode
        self._setup_colormap()

        print(f"Volume final : {self.volume.shape} (Z, Y, X)")
        print(f"Mode de colorisation : {self.colorize_mode}")
        type_text = "Masque de segmentation" if self.is_mask else "Volume d'intensité"
        print(f"Type détecté : {type_text}")

        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle(f"Visualiseur NIfTI - {os.path.basename(self.volume_path)}", fontsize=14)

        # Affichage initial
        self.update_display()

        # Configuration des contrôles
        self.setup_controls()

        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

        plt.show()

    def _prepare_volume_data(self):
        """Prépare les données du volume selon le type détecté"""
        if self.is_mask:
            # Pour les masques, garder les valeurs exactes
            self.volume = self.volume_raw.astype(np.uint8)
        elif self.is_nde_data:
            # Pour les données NDE, normaliser selon la méthode standard
            self.volume = self._normalize_nde_data()
        else:
            # Pour les images, normaliser vers 0-255 si nécessaire
            if self.volume_raw.dtype in [np.float32, np.float64]:
                # Normaliser les données float
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > vmin:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = np.zeros_like(self.volume_raw, dtype=np.uint8)
            elif self.volume_raw.max() > 255:
                # Normaliser les entiers > 255
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                normalized = (self.volume_raw - vmin) / (vmax - vmin)
                self.volume = (normalized * 255).astype(np.uint8)
            else:
                self.volume = self.volume_raw.astype(np.uint8)

    def _normalize_nde_data(self):
        """Normalise les données NDE selon la méthode standard"""
        # Normalisation comme dans gen_endview
        normalized = safe_division(self.volume_raw - self.min_value, self.max_value - self.min_value)

        # Convertir en uint8 pour l'affichage
        return (normalized * 255).astype(np.uint8)

    def _setup_colormap(self):
        """Configure le colormap selon le type de données et le mode choisi"""
        if self.is_mask:
            if self.colorize_mode in ['auto', 'classes']:
                print("🎨 Configuration : Colormap pour classes de segmentation")
                # Colormap pour les masques de segmentation
                self.cmap = ListedColormap([
                    (0, 0, 0),        # 0: background - noir
                    (0, 0, 1),        # 1: frontwall - bleu
                    (0, 1, 0),        # 2: backwall - vert
                    (1, 0, 0),        # 3: flaw - rouge
                    (1, 1, 0)         # 4: indication - jaune
                ])
                self.vmin, self.vmax = 0, 4
            else:
                # Mode grayscale pour masques
                print("🎨 Configuration : Colormap en niveaux de gris pour masque")
                self.cmap = 'gray'
                self.vmin, self.vmax = 0, 4
        elif self.is_nde_data and self.colorize_mode in ['auto', 'rgb']:
            # Données NDE avec colormap OmniScan
            print("🎨 Configuration : Colormap OmniScan pour données NDE")
            try:
                if os.path.exists(self.omniscan_colormap_path):
                    if self.min_value < 0:
                        # Données avec valeurs négatives -> colormap divergent
                        print("🎨 Utilisation du colormap OmniScan divergent (valeurs négatives)")
                        self.cmap = get_omniscan_diverging_colormap(self.omniscan_colormap_path)
                    else:
                        # Données positives -> colormap standard
                        print("🎨 Utilisation du colormap OmniScan standard")
                        omniscan_array = np.load(self.omniscan_colormap_path)
                        self.cmap = ListedColormap(omniscan_array)
                else:
                    print("⚠️ Fichier OmniScanColorMap.npy non trouvé, utilisation de 'viridis'")
                    self.cmap = 'viridis'
            except Exception as e:
                print(f"⚠️ Erreur lors du chargement du colormap OmniScan: {e}")
                self.cmap = 'viridis'
            self.vmin, self.vmax = 0, 255
        else:
            # Volume d'intensité standard ou mode grayscale
            if self.colorize_mode in ['auto', 'grayscale']:
                print("🎨 Configuration : Colormap en niveaux de gris")
                self.cmap = 'gray'
            elif self.colorize_mode == 'rgb':
                print("🎨 Configuration : Colormap RGB (viridis)")
                self.cmap = 'viridis'
            else:
                # Fallback
                self.cmap = 'gray'

            self.vmin, self.vmax = 0, 255

        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle(f"Visualiseur NIfTI - {os.path.basename(self.volume_path)}", fontsize=14)

        # Affichage initial
        self.update_display()

        # Configuration des contrôles
        self.setup_controls()

        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

    def _detect_mask(self, unique_values):
        """Détecte si le volume est un masque de segmentation"""
        # Critères pour détecter un masque :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans la plage 0-4 (ou sous-ensemble)
        # 3. Nombre limité de valeurs uniques (≤ 6)

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return False

        # Vérifier si les valeurs sont dans la plage 0-4
        if unique_values.max() <= 4 and unique_values.min() >= 0:
            # Vérifier si on a un nombre raisonnable de classes
            if len(unique_values) <= 6:
                return True

        return False

    def _detect_nde_data(self):
        """Détecte si les données sont des données NDE (amplitudes)"""
        # Critères pour détecter des données NDE :
        # 1. Plage de valeurs caractéristique des amplitudes
        # 2. Nom du fichier contenant des mots-clés NDE
        # 3. Valeurs entières même si converties en float

        # Si c'est déjà détecté comme masque, ce n'est pas NDE
        if self.is_mask:
            return False

        filename = os.path.basename(self.volume_path).lower()

        # Vérifier les mots-clés dans le nom du fichier
        nde_keywords = ['amplitude', 'endview', 'dscan', 'cscan', 'ascan', 'ultrasonic', 'nde', 'raw_data']
        has_nde_keywords = any(keyword in filename for keyword in nde_keywords)

        # Vérifier la plage de valeurs (typique des amplitudes NDE)
        value_range = self.max_value - self.min_value
        has_nde_range = value_range > 1000 and self.max_value > 10000

        # Vérifier si les valeurs sont entières (même si stockées en float)
        sample_data = self.volume_raw.flatten()[:1000]  # Échantillon pour tester
        are_integers = np.allclose(sample_data, np.round(sample_data))

        # Critères de détection NDE
        is_nde = (has_nde_keywords or has_nde_range) and are_integers and value_range > 100

        if is_nde:
            print(f"🔍 Détection NDE : keywords={has_nde_keywords}, range={has_nde_range}, integers={are_integers}")

        return is_nde

    def setup_controls(self):
        """Configure les contrôles de l'interface"""
        # Ajouter des instructions
        instructions = [
            "CONTRÔLES:",
            "← → : Slice précédente/suivante",
            "Page Up/Down : +/-10 slices",
            "Home/End : Première/dernière slice",
            "Q ou Escape : Quitter",
            "S : Sauvegarder la slice actuelle",
            "I : Informations sur la slice"
        ]

        # Afficher les instructions dans le titre
        control_text = " | ".join(instructions[:3])
        self.ax.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1} | {control_text}",
                         fontsize=10, pad=20)

    def update_display(self):
        """Met à jour l'affichage de la slice actuelle"""
        self.ax.clear()

        # Affichage de la slice
        current_data = self.volume[self.current_slice, :, :]

        im = self.ax.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)

        # Titre avec informations
        title = f"Slice {self.current_slice + 1}/{self.max_slice + 1}"
        if self.is_mask:
            unique_in_slice = np.unique(current_data)
            title += f" | Classes présentes: {unique_in_slice}"
            title += f" | Mode: {'Classes' if self.colorize_mode == 'classes' else 'Grayscale'}"
        elif self.is_nde_data:
            # Pour les données NDE, afficher les valeurs brutes originales
            raw_slice = self.volume_raw[self.current_slice, :, :]
            title += f" | Amplitude: {raw_slice.min():.0f} - {raw_slice.max():.0f}"
            title += f" | Mode: {'OmniScan' if self.colorize_mode == 'rgb' else 'Grayscale'}"
        else:
            title += f" | Min: {current_data.min():.2f}, Max: {current_data.max():.2f}"
            title += f" | Mode: {'RGB' if self.colorize_mode == 'rgb' else 'Grayscale'}"

        self.ax.set_title(title, fontsize=10)
        self.ax.axis('off')

        # Ajouter une colorbar pour les masques en mode classes
        if self.is_mask and self.colorize_mode == 'classes' and not hasattr(self, 'colorbar'):
            self.colorbar = plt.colorbar(im, ax=self.ax, shrink=0.8)
            self.colorbar.set_label('Classes de segmentation')
            self.colorbar.set_ticks([0, 1, 2, 3, 4])
            self.colorbar.set_ticklabels(['Background', 'Frontwall', 'Backwall', 'Flaw', 'Indication'])

        # Instructions en bas
        if self.is_mask:
            instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | C : Basculer colormap | Q : Quitter | S : Sauvegarder | I : Info"
        else:
            instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | C : Basculer colormap | Q : Quitter | S : Sauvegarder | I : Info"
        self.fig.text(0.5, 0.02, instructions_text, ha='center', fontsize=9, style='italic')

        self.fig.canvas.draw()

    def on_key_press(self, event):
        """Gestion des événements clavier"""
        if event.key == 'right' or event.key == 'down':
            self.next_slice()
        elif event.key == 'left' or event.key == 'up':
            self.previous_slice()
        elif event.key == 'pagedown':
            self.jump_slices(10)
        elif event.key == 'pageup':
            self.jump_slices(-10)
        elif event.key == 'home':
            self.go_to_slice(0)
        elif event.key == 'end':
            self.go_to_slice(self.max_slice)
        elif event.key == 'q' or event.key == 'escape':
            plt.close(self.fig)
        elif event.key == 's':
            self.save_current_slice()
        elif event.key == 'i':
            self.show_slice_info()
        elif event.key == 'c':
            self.toggle_colormap()

    def toggle_colormap(self):
        """Bascule entre les modes de colorisation"""
        if self.is_mask:
            # Pour les masques : basculer entre classes et grayscale
            if self.colorize_mode in ['auto', 'classes']:
                self.colorize_mode = 'grayscale'
            else:
                self.colorize_mode = 'classes'
        elif self.is_nde_data:
            # Pour les données NDE : basculer entre omniscan et grayscale
            if self.colorize_mode in ['auto', 'rgb']:
                self.colorize_mode = 'grayscale'
            else:
                self.colorize_mode = 'rgb'
        else:
            # Pour les images : basculer entre grayscale et RGB
            if self.colorize_mode in ['auto', 'grayscale']:
                self.colorize_mode = 'rgb'
            else:
                self.colorize_mode = 'grayscale'

        print(f"🎨 Changement de colormap : {self.colorize_mode}")
        self._setup_colormap()

        # Supprimer l'ancienne colorbar si elle existe
        if hasattr(self, 'colorbar'):
            self.colorbar.remove()
            delattr(self, 'colorbar')

        self.update_display()

    def next_slice(self):
        """Passe à la slice suivante"""
        if self.current_slice < self.max_slice:
            self.current_slice += 1
            self.update_display()
        else:
            print("Déjà à la dernière slice")

    def previous_slice(self):
        """Passe à la slice précédente"""
        if self.current_slice > 0:
            self.current_slice -= 1
            self.update_display()
        else:
            print("Déjà à la première slice")

    def jump_slices(self, n):
        """Saute de n slices"""
        new_slice = self.current_slice + n
        new_slice = max(0, min(new_slice, self.max_slice))
        if new_slice != self.current_slice:
            self.current_slice = new_slice
            self.update_display()

    def go_to_slice(self, slice_num):
        """Va directement à une slice spécifique"""
        slice_num = max(0, min(slice_num, self.max_slice))
        if slice_num != self.current_slice:
            self.current_slice = slice_num
            self.update_display()

    def save_current_slice(self):
        """Sauvegarde la slice actuelle comme image"""
        filename = f"nifti_slice_{self.current_slice:03d}.png"
        current_data = self.volume[self.current_slice, :, :]

        # Créer une figure temporaire pour la sauvegarde
        fig_save, ax_save = plt.subplots(figsize=(8, 6))
        ax_save.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax_save.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1}")
        ax_save.axis('off')

        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig_save)

        print(f"Slice sauvegardée : {filename}")

    def show_slice_info(self):
        """Affiche des informations détaillées sur la slice actuelle"""
        current_data = self.volume[self.current_slice, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)

        print(f"\n=== INFORMATIONS SLICE {self.current_slice + 1} ===")
        print(f"Forme : {current_data.shape}")

        # Éviter les backslashes dans les f-strings
        type_text = "Masque de segmentation" if self.is_mask else "Volume d'intensité"
        print(f"Type : {type_text}")

        print(f"Min/Max : {current_data.min()} / {current_data.max()}")
        print(f"Valeurs uniques et leurs occurrences :")
        for val, count in zip(unique_values, counts):
            percentage = (count / current_data.size) * 100
            if self.is_mask:
                class_names = {0: 'Background', 1: 'Frontwall', 2: 'Backwall', 3: 'Flaw', 4: 'Indication'}
                class_name = class_names.get(int(val), f'Classe {int(val)}')
                print(f"  {class_name} ({int(val)}) : {count} pixels ({percentage:.1f}%)")
            else:
                print(f"  Valeur {val} : {count} pixels ({percentage:.1f}%)")
        print("=" * 40)


# === UTILISATION ===
if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # Utilisation avec argument en ligne de commande
        path_to_nifti = sys.argv[1]
        colorize_mode = sys.argv[2] if len(sys.argv) > 2 else 'auto'
    else:
        # Chemin par défaut
        path_to_nifti = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference\inference_test4labeldifferent_3d_v3\case_001.nii.gz"
        colorize_mode = 'auto'

    # Options de colorisation :
    # 'auto' : Détection automatique (classes pour masques, grayscale pour images)
    # 'grayscale' : Toujours en niveaux de gris
    # 'rgb' : RGB/couleur pour les images (viridis)
    # 'classes' : Couleurs distinctes pour les masques

    print(f"📂 Chargement : {path_to_nifti}")
    print(f"🎨 Mode de colorisation : {colorize_mode}")

    # Vérifier que le fichier existe
    if not os.path.exists(path_to_nifti):
        print(f"❌ Erreur : Le fichier {path_to_nifti} n'existe pas")
        sys.exit(1)

    # Lancement du visualiseur
    viewer = NIfTIVolumeViewer(path_to_nifti, colorize_mode)
