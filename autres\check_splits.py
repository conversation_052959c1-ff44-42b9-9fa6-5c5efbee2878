#!/usr/bin/env python3
"""
Script pour vérifier le contenu du fichier splits_final.json
"""

import json

def check_splits():
    try:
        with open('splits_final.json', 'r') as f:
            data = json.load(f)
        
        print(f"✅ Fichier chargé avec succès")
        print(f"📊 Nombre de folds: {len(data)}")
        
        for i, fold in enumerate(data):
            train_count = len(fold['train'])
            val_count = len(fold['val'])
            total = train_count + val_count
            print(f"📁 Fold {i}: {train_count:,} train + {val_count:,} val = {total:,} total")
        
        # Vérifier qu'il n'y a pas de doublons entre les folds
        all_cases = set()
        for fold in data:
            fold_cases = set(fold['train'] + fold['val'])
            if all_cases:
                if all_cases != fold_cases:
                    print("⚠️ Les folds n'ont pas les mêmes cas (problème potentiel)")
            all_cases = fold_cases
        
        print(f"🎯 Total de cas uniques: {len(all_cases):,}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    check_splits()
