#!/usr/bin/env python3
"""
Test final pour vérifier que l'interface graphique fonctionne correctement
avec view_mask.py après les corrections
"""

import os
import sys
import subprocess
import tempfile

def test_interface_function():
    """Test direct de la fonction de l'interface"""
    print("🧪 Test de la fonction _create_temp_visualize_mask_script")
    print("=" * 60)
    
    # Importer la classe de l'interface
    sys.path.append('.')
    from interface_graphique import ScriptGUI
    import tkinter as tk
    
    # Créer une instance temporaire (sans interface graphique)
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre
    
    try:
        gui = ScriptGUI(root)
        
        # Test avec un chemin personnalisé
        test_path = "test_labels/test_label_000005.png"
        
        if not os.path.exists(test_path):
            print(f"❌ Fichier de test non trouvé: {test_path}")
            return False
        
        print(f"📁 Test avec le fichier: {test_path}")
        
        # Simuler la fonction append_output pour capturer les logs
        debug_logs = []
        original_append = gui.append_output
        gui.append_output = lambda msg: debug_logs.append(msg.strip())
        
        # Appeler la fonction
        temp_script = gui._create_temp_visualize_mask_script(test_path)
        
        # Afficher les logs de débogage
        print("📋 Logs de débogage:")
        for log in debug_logs:
            print(f"   {log}")
        
        # Vérifier que le script temporaire existe
        if os.path.exists(temp_script):
            print(f"✅ Script temporaire créé: {temp_script}")
            
            # Vérifier le contenu
            with open(temp_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if test_path in content:
                print(f"✅ Chemin correctement intégré dans le script")
                
                # Tester l'exécution
                print("🚀 Test d'exécution du script temporaire...")
                result = subprocess.run([sys.executable, temp_script], 
                                      capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    print("✅ Script exécuté avec succès!")
                    print("📤 Sortie:")
                    print(result.stdout)
                else:
                    print("❌ Erreur lors de l'exécution:")
                    print("STDERR:", result.stderr)
                
                # Nettoyer
                os.remove(temp_script)
                return result.returncode == 0
            else:
                print(f"❌ Chemin NON trouvé dans le script!")
                print("Contenu du script:")
                print(content[:500] + "..." if len(content) > 500 else content)
                os.remove(temp_script)
                return False
        else:
            print(f"❌ Script temporaire non créé!")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()

def create_test_instructions():
    """Créer des instructions de test pour l'utilisateur"""
    print("\n" + "=" * 60)
    print("📋 INSTRUCTIONS DE TEST MANUEL")
    print("=" * 60)
    print("1. Lancez l'interface graphique:")
    print("   python interface_graphique.py")
    print()
    print("2. Sélectionnez:")
    print("   - Catégorie: 'Utilitaire'")
    print("   - Script: 'view_mask.py'")
    print()
    print("3. Cliquez sur 'Parcourir' et sélectionnez:")
    print("   test_labels/test_label_000000.png")
    print()
    print("4. Cliquez sur 'Exécuter'")
    print()
    print("5. Vérifiez dans la sortie:")
    print("   - [DEBUG] messages montrant le bon chemin")
    print("   - 'Valeurs uniques dans l'image: [0 1 2 3 4]'")
    print("   - PAS d'erreur 'can't open/read file'")
    print()
    print("Si vous voyez encore l'ancien chemin, redémarrez complètement")
    print("l'interface graphique (fermez et relancez).")

if __name__ == "__main__":
    print("🔧 Test Final - Interface Graphique view_mask.py")
    print("=" * 60)
    
    success = test_interface_function()
    
    create_test_instructions()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ SUCCÈS: La correction fonctionne!")
        print("L'interface graphique devrait maintenant utiliser le bon chemin.")
    else:
        print("❌ ÉCHEC: Il y a encore des problèmes.")
        print("Vérifiez les logs ci-dessus pour plus de détails.")
    print("=" * 60)
