#!/usr/bin/env python3
"""
Script d'export en lot de fichiers H5 vers images PNG
Traite tous les fichiers H5 d'un dossier et les exporte en images PNG
"""

import os
import sys
import argparse
from pathlib import Path
import glob
from h5_export_png import H5ToPNGExporter

class H5FolderToPNGExporter:
    def __init__(self, input_folder, output_folder, prefix="slice", image_type="auto", export_uint8=False,
                 rotation=0, omniscan_colormap_path='./OmniScanColorMap.npy'):
        """
        Exporte tous les fichiers H5 d'un dossier en images PNG

        Args:
            input_folder: Dossier contenant les fichiers H5
            output_folder: Dossier de sortie pour les images PNG
            prefix: Préfixe pour les noms de fichiers (défaut: "slice")
            image_type: Type d'images ("auto", "imagesTr", "labelsTr")
            export_uint8: Si True, export en uint8, sinon RGB/couleurs OmniScan
            rotation: Rotation à appliquer en degrés (0, 90, 180, 270)
            omniscan_colormap_path: Chemin vers le fichier OmniScanColorMap.npy
        """
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        self.prefix = prefix
        self.image_type = image_type
        self.export_uint8 = export_uint8
        self.rotation = rotation
        self.omniscan_colormap_path = omniscan_colormap_path
        
        # Créer le dossier de sortie
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
        print(f"📂 Dossier d'entrée : {self.input_folder}")
        print(f"📁 Dossier de sortie : {self.output_folder}")
        print(f"🏷️ Type d'images : {self.image_type}")
        print(f"🎨 Mode export : {'uint8' if self.export_uint8 else 'RGB/Couleurs OmniScan'}")
    
    def find_h5_files(self):
        """Trouve tous les fichiers H5 dans le dossier d'entrée"""
        h5_files = []
        
        # Chercher les fichiers .h5 et .hdf5
        for pattern in ["*.h5", "*.hdf5"]:
            files = list(self.input_folder.glob(pattern))
            h5_files.extend(files)
        
        # Trier par nom
        h5_files.sort()
        
        print(f"📊 {len(h5_files)} fichiers H5 trouvés")
        for i, file_path in enumerate(h5_files, 1):
            print(f"  {i:2d}. {file_path.name}")
        
        return h5_files
    
    def export_all_files(self, dpi=None):
        """Exporte tous les fichiers H5 trouvés"""
        h5_files = self.find_h5_files()

        if not h5_files:
            print("❌ Aucun fichier H5 trouvé dans le dossier")
            return

        print(f"\n🚀 Début de l'export de {len(h5_files)} fichiers...")

        successful_exports = 0
        failed_exports = 0
        skipped_exports = 0

        for i, h5_file in enumerate(h5_files, 1):
            print(f"\n{'='*60}")
            print(f"📄 Fichier {i}/{len(h5_files)} : {h5_file.name}")
            print(f"{'='*60}")

            # Créer un sous-dossier pour ce fichier
            file_stem = h5_file.stem  # Nom sans extension
            file_output_dir = self.output_folder / file_stem

            # Vérifier si le dossier existe déjà et contient des images
            if file_output_dir.exists():
                existing_images = list(file_output_dir.glob("*.png"))
                if existing_images:
                    print(f"⏭️ Fichier déjà traité ({len(existing_images)} images trouvées) - SKIP")
                    skipped_exports += 1
                    continue
                else:
                    print(f"🔄 Dossier existe mais vide - Retraitement")

            try:
                file_output_dir.mkdir(exist_ok=True)
                
                # Déterminer le DPI si non spécifié
                if dpi is None:
                    # Résolution automatique basée sur les dimensions
                    import h5py
                    with h5py.File(h5_file, 'r') as h5_file_handle:
                        # Trouver le premier dataset pour estimer les dimensions
                        def find_first_dataset(group, path=""):
                            for key in group.keys():
                                item = group[key]
                                current_path = f"{path}/{key}" if path else key
                                if isinstance(item, h5py.Group):
                                    result = find_first_dataset(item, current_path)
                                    if result:
                                        return result
                                elif isinstance(item, h5py.Dataset) and len(item.shape) >= 2:
                                    return current_path
                            return None
                        
                        first_dataset_path = find_first_dataset(h5_file_handle)
                        if first_dataset_path:
                            temp_volume = h5_file_handle[first_dataset_path]
                            if temp_volume.ndim >= 2:
                                avg_dim = (temp_volume.shape[-2] + temp_volume.shape[-1]) / 2
                                if avg_dim > 512:
                                    file_dpi = 300
                                elif avg_dim > 256:
                                    file_dpi = 200
                                else:
                                    file_dpi = 150
                            else:
                                file_dpi = 150
                        else:
                            file_dpi = 150
                else:
                    file_dpi = dpi
                
                # Créer l'exporteur pour ce fichier
                exporter = H5ToPNGExporter(
                    h5_path=str(h5_file),
                    output_dir=str(file_output_dir),
                    prefix=self.prefix,
                    image_type=self.image_type,
                    export_uint8=self.export_uint8,
                    rotation=self.rotation,
                    omniscan_colormap_path=self.omniscan_colormap_path
                )
                
                # Exporter toutes les slices
                exporter.export_all_slices(dpi=file_dpi)

                # Nettoyage de mémoire
                del exporter
                import gc
                gc.collect()

                successful_exports += 1
                print(f"✅ Export réussi pour {h5_file.name}")

            except KeyboardInterrupt:
                print(f"\n⚠️ Export interrompu par l'utilisateur au fichier {h5_file.name}")
                print(f"Pour reprendre, utilisez : --start-from \"{h5_file.name}\"")
                break
            except MemoryError:
                failed_exports += 1
                print(f"❌ Erreur de mémoire lors de l'export de {h5_file.name}")
                print("💡 Essayez de fermer d'autres applications ou de traiter moins de fichiers à la fois")
                # Nettoyage de mémoire forcé
                import gc
                gc.collect()
            except Exception as e:
                failed_exports += 1
                print(f"❌ Erreur lors de l'export de {h5_file.name}: {e}")
                import traceback
                traceback.print_exc()
                # Nettoyage de mémoire en cas d'erreur
                import gc
                gc.collect()
        
        # Résumé final
        print(f"\n{'='*60}")
        print(f"📊 RÉSUMÉ DE L'EXPORT")
        print(f"{'='*60}")
        print(f"✅ Exports réussis : {successful_exports}")
        print(f"⏭️ Fichiers skippés : {skipped_exports}")
        print(f"❌ Exports échoués : {failed_exports}")
        print(f"📁 Images sauvegardées dans : {self.output_folder}")

        if successful_exports > 0:
            print(f"\n🎉 Export terminé avec succès !")
        elif skipped_exports == len(h5_files):
            print(f"\n✨ Tous les fichiers étaient déjà traités !")
        else:
            print(f"\n⚠️ Aucun export réussi")

def main():
    parser = argparse.ArgumentParser(description="Export en lot de fichiers H5 vers images PNG")
    parser.add_argument("input_folder", help="Dossier contenant les fichiers H5")
    parser.add_argument("output_folder", help="Dossier de sortie pour les images PNG")
    parser.add_argument("--prefix", default="slice", help="Préfixe pour les noms de fichiers (défaut: slice)")
    parser.add_argument("--image-type", choices=["auto", "imagesTr", "labelsTr"], default="auto", 
                       help="Type d'images (défaut: auto)")
    parser.add_argument("--uint8", action="store_true", help="Export en uint8 (sinon RGB/couleurs OmniScan)")
    parser.add_argument("--dpi", type=int, help="Résolution des images (défaut: résolution du volume)")
    parser.add_argument("--rotation", type=int, choices=[0, 90, 180, 270], default=0,
                       help="Rotation à appliquer en degrés (défaut: 0)")
    parser.add_argument("--omniscan-colormap", default="./OmniScanColorMap.npy",
                       help="Chemin vers le fichier OmniScanColorMap.npy")

    args = parser.parse_args()
    
    # Vérifier que le dossier d'entrée existe
    if not os.path.exists(args.input_folder):
        print(f"❌ Erreur : Le dossier d'entrée '{args.input_folder}' n'existe pas")
        sys.exit(1)
    
    try:
        # Créer l'exporteur de dossier
        folder_exporter = H5FolderToPNGExporter(
            input_folder=args.input_folder,
            output_folder=args.output_folder,
            prefix=args.prefix,
            image_type=args.image_type,
            export_uint8=args.uint8,
            rotation=args.rotation,
            omniscan_colormap_path=args.omniscan_colormap
        )
        
        # Exporter tous les fichiers
        folder_exporter.export_all_files(dpi=args.dpi)
        
    except KeyboardInterrupt:
        print("\n⚠️ Export interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erreur générale : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
