import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches

class NPZVolumeViewer:
    def __init__(self, npz_path):
        # Chargement du volume NPZ
        self.npz_data = np.load(npz_path)
        self.npz_path = npz_path
        
        # Afficher les clés disponibles dans le fichier NPZ
        print(f"Fichier NPZ chargé : {npz_path}")
        print(f"Clés disponibles : {list(self.npz_data.keys())}")

        # Sélection automatique intelligente de la meilleure clé
        self.data_key = self._select_best_key()
        print(f"[KEY] Cle selectionnee : '{self.data_key}'")

        # Charger les données du volume
        self.volume_raw = self.npz_data[self.data_key]

        # Convertir en uint8 si nécessaire pour l'affichage
        if self.volume_raw.dtype != np.uint8:
            if self.volume_raw.max() <= 1.0:
                # Données normalisées entre 0 et 1
                self.volume = (self.volume_raw * 255).astype(np.uint8)
            else:
                # Normaliser les données
                volume_min = self.volume_raw.min()
                volume_max = self.volume_raw.max()
                if volume_max > volume_min:
                    self.volume = ((self.volume_raw - volume_min) / (volume_max - volume_min) * 255).astype(np.uint8)
                else:
                    self.volume = self.volume_raw.astype(np.uint8)
        else:
            self.volume = self.volume_raw.astype(np.uint8)

        # Assurer que le volume a au moins 3 dimensions
        if self.volume.ndim == 2:
            # Si c'est une image 2D, l'ajouter comme une seule slice
            self.volume = self.volume[np.newaxis, :, :]
        elif self.volume.ndim > 3:
            # Si plus de 3 dimensions, prendre les 3 premières
            print(f"Volume avec {self.volume.ndim} dimensions détecté. Utilisation des 3 premières dimensions.")
            self.volume = self.volume[:, :, :] if self.volume.ndim == 4 else self.volume

        self.current_slice = 0
        self.max_slice = self.volume.shape[0] - 1

        print(f"Volume chargé : {self.volume.shape} (Z, Y, X)")
        print(f"Valeurs uniques dans le volume : {np.unique(self.volume)}")

        # Détection automatique du type de données
        unique_values = np.unique(self.volume)
        self.is_mask = self._detect_mask(unique_values)

        if self.is_mask:
            print("Détection : Volume de masque/segmentation détecté")
            print("Application du colormap pour les classes 0-4")
            # Colormap pour les masques de segmentation
            self.cmap = ListedColormap([
                (0, 0, 0),        # 0: background - noir
                (0, 0, 1),        # 1: frontwall - bleu
                (0, 1, 0),        # 2: backwall - vert
                (1, 0, 0),        # 3: flaw - rouge
                (1, 1, 0)         # 4: indication - jaune
            ])
            self.vmin, self.vmax = 0, 4
        else:
            print("Détection : Volume d'intensité standard détecté")
            print("Application du colormap en niveaux de gris")
            self.cmap = 'gray'
            self.vmin, self.vmax = self.volume.min(), self.volume.max()

        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle(f"Visualiseur de Volume NPZ - {npz_path.split('/')[-1]} - Clé: {self.data_key}", fontsize=14)

        # Affichage initial
        self.update_display()

        # Configuration des contrôles
        self.setup_controls()

        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

        plt.show()

    def _select_best_key(self):
        """Sélectionne automatiquement la meilleure clé de données à utiliser"""
        available_keys = list(self.npz_data.keys())

        # Sélection automatique
        if len(available_keys) == 1:
            return available_keys[0]

        # Plusieurs clés disponibles - sélection automatique intelligente
        print("Plusieurs clés disponibles. Sélection automatique de la meilleure clé :")

        # Analyser chaque clé pour trouver la meilleure
        candidates = []
        for key in available_keys:
            data = self.npz_data[key]
            shape = data.shape
            dtype = data.dtype

            print(f"  [DATA] '{key}' - Shape: {shape}, Type: {dtype}")

            # Critères de sélection (par ordre de priorité) :
            # 1. Doit être 3D (Z, Y, X)
            # 2. Préférer uint8
            # 3. Éviter les types booléens
            # 4. Éviter les tableaux 1D

            score = 0

            if len(shape) == 3:
                score += 100  # Priorité maximale pour 3D

                if dtype == 'uint8':
                    score += 50  # Bonus pour uint8
                elif dtype in ['uint16', 'int16', 'uint32', 'int32']:
                    score += 30  # Bonus moyen pour autres entiers
                elif dtype in ['float32', 'float64']:
                    score += 10  # Bonus faible pour float

                if dtype != 'bool':
                    score += 20  # Bonus pour non-booléen

            elif len(shape) == 2:
                score += 50  # Acceptable pour 2D
            elif len(shape) == 1:
                score += 1   # Très faible priorité pour 1D

            candidates.append((key, score, shape, dtype))

        # Trier par score décroissant
        candidates.sort(key=lambda x: x[1], reverse=True)

        if candidates and candidates[0][1] > 0:
            selected_key = candidates[0][0]
            print(f"[AUTO] Selection automatique de la meilleure cle : '{selected_key}'")
            print(f"   Shape: {candidates[0][2]}, Type: {candidates[0][3]}")
            return selected_key
        else:
            print("[WARNING] Aucune cle appropriee trouvee (pas de donnees 3D)")
            raise ValueError("Aucune cle appropriee trouvee dans le fichier NPZ")

    def _detect_mask(self, unique_values):
        """Détecte si le volume est un masque de segmentation"""
        # Critères pour détecter un masque :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans la plage 0-4 (ou sous-ensemble)
        # 3. Nombre limité de valeurs uniques (≤ 6)

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return False

        # Vérifier si les valeurs sont dans la plage 0-4
        if unique_values.max() <= 4 and unique_values.min() >= 0:
            # Vérifier si on a un nombre raisonnable de classes
            if len(unique_values) <= 6:
                return True

        return False

    def setup_controls(self):
        """Configure les contrôles de l'interface"""
        # Ajouter des instructions
        instructions = [
            "CONTRÔLES:",
            "← → : Slice précédente/suivante",
            "Page Up/Down : +/-10 slices",
            "Home/End : Première/dernière slice",
            "Q ou Escape : Quitter",
            "S : Sauvegarder la slice actuelle",
            "I : Informations sur la slice"
        ]

        # Afficher les instructions dans le titre
        control_text = " | ".join(instructions[:3])
        self.ax.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1} | {control_text}",
                         fontsize=10, pad=20)

    def update_display(self):
        """Met à jour l'affichage de la slice actuelle"""
        self.ax.clear()

        # Affichage de la slice
        current_data = self.volume[self.current_slice, :, :]

        im = self.ax.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)

        # Titre avec informations
        title = f"Slice {self.current_slice + 1}/{self.max_slice + 1}"
        if self.is_mask:
            unique_in_slice = np.unique(current_data)
            title += f" | Classes présentes: {unique_in_slice}"
        else:
            title += f" | Min: {current_data.min():.2f}, Max: {current_data.max():.2f}"

        self.ax.set_title(title, fontsize=10)
        self.ax.axis('off')

        # Ajouter une colorbar pour les masques
        if self.is_mask and not hasattr(self, 'colorbar'):
            self.colorbar = plt.colorbar(im, ax=self.ax, shrink=0.8)
            self.colorbar.set_label('Classes de segmentation')
            self.colorbar.set_ticks([0, 1, 2, 3, 4])
            self.colorbar.set_ticklabels(['Background', 'Frontwall', 'Backwall', 'Flaw', 'Indication'])

        # Instructions en bas
        instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | Q : Quitter | S : Sauvegarder | I : Info"
        self.fig.text(0.5, 0.02, instructions_text, ha='center', fontsize=9, style='italic')

        self.fig.canvas.draw()

    def on_key_press(self, event):
        """Gestion des événements clavier"""
        if event.key == 'right' or event.key == 'down':
            self.next_slice()
        elif event.key == 'left' or event.key == 'up':
            self.previous_slice()
        elif event.key == 'pagedown':
            self.jump_slices(10)
        elif event.key == 'pageup':
            self.jump_slices(-10)
        elif event.key == 'home':
            self.go_to_slice(0)
        elif event.key == 'end':
            self.go_to_slice(self.max_slice)
        elif event.key == 'q' or event.key == 'escape':
            plt.close(self.fig)
        elif event.key == 's':
            self.save_current_slice()
        elif event.key == 'i':
            self.show_slice_info()

    def next_slice(self):
        """Passe à la slice suivante"""
        if self.current_slice < self.max_slice:
            self.current_slice += 1
            self.update_display()
        else:
            print("Déjà à la dernière slice")

    def previous_slice(self):
        """Passe à la slice précédente"""
        if self.current_slice > 0:
            self.current_slice -= 1
            self.update_display()
        else:
            print("Déjà à la première slice")

    def jump_slices(self, n):
        """Saute de n slices"""
        new_slice = self.current_slice + n
        new_slice = max(0, min(new_slice, self.max_slice))
        if new_slice != self.current_slice:
            self.current_slice = new_slice
            self.update_display()

    def go_to_slice(self, slice_num):
        """Va directement à une slice spécifique"""
        slice_num = max(0, min(slice_num, self.max_slice))
        if slice_num != self.current_slice:
            self.current_slice = slice_num
            self.update_display()

    def save_current_slice(self):
        """Sauvegarde la slice actuelle comme image"""
        import os
        filename = f"npz_slice_{self.current_slice:03d}.png"
        current_data = self.volume[self.current_slice, :, :]

        # Créer une figure temporaire pour la sauvegarde
        fig_save, ax_save = plt.subplots(figsize=(8, 6))
        ax_save.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax_save.set_title(f"NPZ Slice {self.current_slice + 1}/{self.max_slice + 1} - Clé: {self.data_key}")
        ax_save.axis('off')

        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig_save)

        print(f"Slice sauvegardée : {filename}")

    def show_slice_info(self):
        """Affiche des informations détaillées sur la slice actuelle"""
        current_data = self.volume[self.current_slice, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)

        print(f"\n=== INFORMATIONS SLICE {self.current_slice + 1} ===")
        print(f"Fichier NPZ : {self.npz_path}")
        print(f"Clé utilisée : {self.data_key}")
        print(f"Forme : {current_data.shape}")

        # Éviter les backslashes dans les f-strings
        type_text = "Masque de segmentation" if self.is_mask else "Volume d'intensité"
        print(f"Type : {type_text}")

        print(f"Min/Max : {current_data.min()} / {current_data.max()}")
        print(f"Valeurs uniques et leurs occurrences :")
        for val, count in zip(unique_values, counts):
            percentage = (count / current_data.size) * 100
            if self.is_mask:
                class_names = {0: 'Background', 1: 'Frontwall', 2: 'Backwall', 3: 'Flaw', 4: 'Indication'}
                class_name = class_names.get(int(val), f'Classe {int(val)}')
                print(f"  {class_name} ({int(val)}) : {count} pixels ({percentage:.1f}%)")
            else:
                print(f"  Valeur {val} : {count} pixels ({percentage:.1f}%)")
        print("=" * 40)


# === UTILISATION ===
if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("Usage: python npz_view_volume.py <chemin_vers_fichier.npz>")
        sys.exit(1)

    path_to_npz = sys.argv[1]

    # Vérifier que le fichier existe
    import os
    if not os.path.exists(path_to_npz):
        print(f"❌ Erreur : Le fichier {path_to_npz} n'existe pas")
        sys.exit(1)

    # Lancement du visualiseur
    viewer = NPZVolumeViewer(path_to_npz)
