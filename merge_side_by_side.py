import os
from PIL import Image

# === Dossiers en entrée/sortie (hardcodés) ===
FOLDER_A = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference\TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data\dataset original"
FOLDER_B = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference\TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data\overlays"
OUTPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Results\inference\TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data\comparaison"

# === Création du dossier de sortie si inexistant ===
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# === Récupérer les fichiers PNG communs aux deux dossiers ===
files_a = {f for f in os.listdir(FOLDER_A) if f.endswith('.png')}
files_b = {f for f in os.listdir(FOLDER_B) if f.endswith('.png')}
common_files = sorted(files_a & files_b)

# === Traitement des images ===
for filename in common_files:
    path_a = os.path.join(FOLDER_A, filename)
    path_b = os.path.join(FOLDER_B, filename)

    img_a = Image.open(path_a)
    img_b = Image.open(path_b)

    # Redimensionner si nécessaire (optionnel)
    if img_a.size != img_b.size:
        img_b = img_b.resize(img_a.size)

    # Créer une nouvelle image côte à côte
    total_width = img_a.width + img_b.width
    max_height = max(img_a.height, img_b.height)
    new_img = Image.new('RGB', (total_width, max_height))

    new_img.paste(img_a, (0, 0))
    new_img.paste(img_b, (img_a.width, 0))

    # Sauvegarde
    output_path = os.path.join(OUTPUT_FOLDER, filename)
    new_img.save(output_path)

    print(f"[✔] Image sauvegardée : {output_path}")

print("✅ Fusion terminée.")
