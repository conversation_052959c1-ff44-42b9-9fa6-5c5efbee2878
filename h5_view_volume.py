import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches
import os
from pathlib import Path

# Import des utilitaires pour les couleurs NDE
try:
    from utils.omniscan_div_cmap import get_omniscan_diverging_colormap
    from utils.utils_linear_scan_alongv import safe_division
except ImportError:
    print("⚠️ Modules utils non trouvés, utilisation des colormaps par défaut")
    def get_omniscan_diverging_colormap(path):
        return plt.cm.viridis
    def safe_division(num, den):
        return num / den if den != 0 else 0.0

class H5VolumeViewer:
    def __init__(self, h5_path, colorize_mode='auto', omniscan_colormap_path='./OmniScanColorMap.npy'):
        # Chargement du fichier H5
        self.h5_file = h5py.File(h5_path, 'r')
        self.h5_path = h5_path
        self.colorize_mode = colorize_mode  # 'auto', 'grayscale', 'omniscan'
        self.omniscan_colormap_path = omniscan_colormap_path
        
        # Afficher les clés disponibles dans le fichier H5
        print(f"Fichier H5 chargé : {h5_path}")
        self._print_h5_structure(self.h5_file, max_depth=2)
        
        # Sélectionner automatiquement le dataset ou demander à l'utilisateur
        datasets = self._find_datasets(self.h5_file)
        
        if len(datasets) == 0:
            raise ValueError("Aucun dataset trouvé dans le fichier H5")
        elif len(datasets) == 1:
            self.dataset_path = datasets[0]
            print(f"Utilisation automatique du dataset : '{self.dataset_path}'")
        else:
            print("Plusieurs datasets disponibles. Sélectionnez un dataset :")
            for i, dataset_path in enumerate(datasets):
                dataset = self.h5_file[dataset_path]
                shape = dataset.shape
                dtype = dataset.dtype
                print(f"  {i}: '{dataset_path}' - Shape: {shape}, Type: {dtype}")
            
            while True:
                try:
                    choice = input("Entrez le numéro du dataset à visualiser (ou le chemin complet) : ").strip()
                    if choice.isdigit():
                        idx = int(choice)
                        if 0 <= idx < len(datasets):
                            self.dataset_path = datasets[idx]
                            break
                    elif choice in datasets:
                        self.dataset_path = choice
                        break
                    else:
                        print("Choix invalide. Veuillez réessayer.")
                except (ValueError, IndexError):
                    print("Choix invalide. Veuillez réessayer.")
        
        # Charger les données du volume
        dataset = self.h5_file[self.dataset_path]
        self.volume_raw = np.array(dataset)  # Garder les données originales

        # Analyser les données pour déterminer le type
        self.min_value = float(np.min(self.volume_raw))
        self.max_value = float(np.max(self.volume_raw))
        self.is_nde_data = self._detect_nde_data()

        print(f"📊 Données brutes : min={self.min_value:.2f}, max={self.max_value:.2f}")
        print(f"📊 Type détecté : {'Données NDE' if self.is_nde_data else 'Données génériques'}")

        # Normaliser et convertir selon le type
        if self.is_nde_data:
            self.volume = self._normalize_nde_data()
        else:
            # Pour les données non-NDE, conversion simple en uint8
            if self.volume_raw.dtype in [np.int16, np.uint16, np.int32, np.uint32]:
                # Normaliser vers 0-255
                normalized = (self.volume_raw - self.min_value) / (self.max_value - self.min_value)
                self.volume = (normalized * 255).astype(np.uint8)
            else:
                self.volume = self.volume_raw.astype(np.uint8)
        
        # Assurer que le volume a au moins 3 dimensions
        if self.volume.ndim == 2:
            # Si c'est une image 2D, l'ajouter comme une seule slice
            self.volume = self.volume[np.newaxis, :, :]
        elif self.volume.ndim > 3:
            # Si plus de 3 dimensions, prendre les 3 premières
            print(f"Volume avec {self.volume.ndim} dimensions détecté. Utilisation des 3 premières dimensions.")
            self.volume = self.volume[:, :, :] if self.volume.ndim == 4 else self.volume
        
        self.current_slice = 0
        self.max_slice = self.volume.shape[0] - 1

        print(f"Volume chargé : {self.volume.shape} (Z, Y, X)")
        print(f"Valeurs uniques dans le volume : {np.unique(self.volume)}")

        # Configuration du colormap selon le type de données et le mode
        self._setup_colormap()

        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.fig.suptitle(f"Visualiseur de Volume H5 - {h5_path.split('/')[-1]} - Dataset: {self.dataset_path}", fontsize=14)

        # Affichage initial
        self.update_display()

        # Configuration des contrôles
        self.setup_controls()

        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)

        plt.show()

    def _print_h5_structure(self, group, prefix="", max_depth=3, current_depth=0):
        """Affiche la structure du fichier H5"""
        if current_depth >= max_depth:
            return
            
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Group):
                print(f"{prefix}📁 Groupe: {key}/")
                self._print_h5_structure(item, prefix + "  ", max_depth, current_depth + 1)
            elif isinstance(item, h5py.Dataset):
                print(f"{prefix}📄 Dataset: {key} - Shape: {item.shape}, Type: {item.dtype}")

    def _find_datasets(self, group, path=""):
        """Trouve tous les datasets dans le fichier H5"""
        datasets = []
        for key in group.keys():
            item = group[key]
            current_path = f"{path}/{key}" if path else key
            if isinstance(item, h5py.Group):
                datasets.extend(self._find_datasets(item, current_path))
            elif isinstance(item, h5py.Dataset):
                # Filtrer les datasets qui ressemblent à des volumes (au moins 2D)
                if len(item.shape) >= 2:
                    datasets.append(current_path)
        return datasets

    def _detect_nde_data(self):
        """Détecte si les données sont des données NDE (amplitudes)"""
        # Critères pour détecter des données NDE :
        # 1. Type de données int16/uint16 typique des amplitudes
        # 2. Plage de valeurs caractéristique
        # 3. Nom du dataset contenant des mots-clés NDE

        dtype = self.volume_raw.dtype
        dataset_name = self.dataset_path.lower()

        # Vérifier le type de données
        is_amplitude_type = dtype in [np.int16, np.uint16, np.int32, np.uint32]

        # Vérifier les mots-clés dans le nom du dataset
        nde_keywords = ['amplitude', 'endview', 'dscan', 'cscan', 'ascan', 'ultrasonic', 'nde']
        has_nde_keywords = any(keyword in dataset_name for keyword in nde_keywords)

        # Vérifier la plage de valeurs (typique des amplitudes NDE)
        value_range = self.max_value - self.min_value
        has_nde_range = value_range > 100 and (self.max_value > 1000 or self.min_value < -100)

        return is_amplitude_type and (has_nde_keywords or has_nde_range)

    def _normalize_nde_data(self):
        """Normalise les données NDE selon la méthode standard"""
        # Normalisation comme dans gen_endview
        normalized = safe_division(self.volume_raw - self.min_value, self.max_value - self.min_value)

        # Convertir en uint8 pour l'affichage
        return (normalized * 255).astype(np.uint8)

    def _setup_colormap(self):
        """Configure le colormap selon le type de données et le mode choisi"""
        if self.is_nde_data and self.colorize_mode in ['auto', 'omniscan']:
            print("🎨 Configuration : Colormap OmniScan pour données NDE")
            try:
                if os.path.exists(self.omniscan_colormap_path):
                    if self.min_value < 0:
                        # Données avec valeurs négatives -> colormap divergent
                        print("🎨 Utilisation du colormap OmniScan divergent (valeurs négatives)")
                        self.cmap = get_omniscan_diverging_colormap(self.omniscan_colormap_path)
                    else:
                        # Données positives -> colormap standard
                        print("🎨 Utilisation du colormap OmniScan standard")
                        omniscan_array = np.load(self.omniscan_colormap_path)
                        self.cmap = ListedColormap(omniscan_array)
                else:
                    print("⚠️ Fichier OmniScanColorMap.npy non trouvé, utilisation de 'viridis'")
                    self.cmap = 'viridis'
            except Exception as e:
                print(f"⚠️ Erreur lors du chargement du colormap OmniScan: {e}")
                self.cmap = 'viridis'
        else:
            # Mode grayscale ou données non-NDE
            print("🎨 Configuration : Colormap en niveaux de gris")
            self.cmap = 'gray'

        # Définir les limites de valeurs pour l'affichage
        self.vmin, self.vmax = 0, 255

    def _detect_mask(self, unique_values):
        """Détecte si le volume est un masque de segmentation"""
        # Pour les données NDE, on ne considère généralement pas comme des masques
        if self.is_nde_data:
            return False

        # Critères pour détecter un masque :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans la plage 0-4 (ou sous-ensemble)
        # 3. Nombre limité de valeurs uniques (≤ 6)

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return False

        # Vérifier si les valeurs sont dans la plage 0-4
        if unique_values.max() <= 4 and unique_values.min() >= 0:
            # Vérifier si on a un nombre raisonnable de classes
            if len(unique_values) <= 6:
                return True

        return False

    def setup_controls(self):
        """Configure les contrôles de l'interface"""
        # Ajouter des instructions
        instructions = [
            "CONTRÔLES:",
            "← → : Slice précédente/suivante",
            "Page Up/Down : +/-10 slices",
            "Home/End : Première/dernière slice",
            "Q ou Escape : Quitter",
            "S : Sauvegarder la slice actuelle",
            "I : Informations sur la slice"
        ]

        # Afficher les instructions dans le titre
        control_text = " | ".join(instructions[:3])
        self.ax.set_title(f"Slice {self.current_slice + 1}/{self.max_slice + 1} | {control_text}",
                         fontsize=10, pad=20)

    def update_display(self):
        """Met à jour l'affichage de la slice actuelle"""
        self.ax.clear()

        # Affichage de la slice
        current_data = self.volume[self.current_slice, :, :]

        im = self.ax.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)

        # Titre avec informations
        title = f"Slice {self.current_slice + 1}/{self.max_slice + 1}"
        if self.is_nde_data:
            # Pour les données NDE, afficher les valeurs brutes originales
            raw_slice = self.volume_raw[self.current_slice, :, :]
            title += f" | Amplitude: {raw_slice.min():.0f} - {raw_slice.max():.0f}"
            title += f" | Mode: {'OmniScan' if self.colorize_mode == 'omniscan' else 'Grayscale'}"
        else:
            title += f" | Min: {current_data.min():.2f}, Max: {current_data.max():.2f}"

        self.ax.set_title(title, fontsize=10)
        self.ax.axis('off')

        # Instructions en bas (avec nouveaux contrôles)
        if self.is_nde_data:
            instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | C : Changer colormap | Q : Quitter | S : Sauvegarder | I : Info"
        else:
            instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | Q : Quitter | S : Sauvegarder | I : Info"
        self.fig.text(0.5, 0.02, instructions_text, ha='center', fontsize=9, style='italic')

        self.fig.canvas.draw()

    def on_key_press(self, event):
        """Gestion des événements clavier"""
        if event.key == 'right' or event.key == 'down':
            self.next_slice()
        elif event.key == 'left' or event.key == 'up':
            self.previous_slice()
        elif event.key == 'pagedown':
            self.jump_slices(10)
        elif event.key == 'pageup':
            self.jump_slices(-10)
        elif event.key == 'home':
            self.go_to_slice(0)
        elif event.key == 'end':
            self.go_to_slice(self.max_slice)
        elif event.key == 'q' or event.key == 'escape':
            self.h5_file.close()
            plt.close(self.fig)
        elif event.key == 's':
            self.save_current_slice()
        elif event.key == 'i':
            self.show_slice_info()
        elif event.key == 'c' and self.is_nde_data:
            self.toggle_colormap()

    def next_slice(self):
        """Passe à la slice suivante"""
        if self.current_slice < self.max_slice:
            self.current_slice += 1
            self.update_display()
        else:
            print("Déjà à la dernière slice")

    def previous_slice(self):
        """Passe à la slice précédente"""
        if self.current_slice > 0:
            self.current_slice -= 1
            self.update_display()
        else:
            print("Déjà à la première slice")

    def jump_slices(self, n):
        """Saute de n slices"""
        new_slice = self.current_slice + n
        new_slice = max(0, min(new_slice, self.max_slice))
        if new_slice != self.current_slice:
            self.current_slice = new_slice
            self.update_display()

    def go_to_slice(self, slice_num):
        """Va directement à une slice spécifique"""
        slice_num = max(0, min(slice_num, self.max_slice))
        if slice_num != self.current_slice:
            self.current_slice = slice_num
            self.update_display()

    def toggle_colormap(self):
        """Bascule entre les modes de colorisation pour les données NDE"""
        if not self.is_nde_data:
            return

        if self.colorize_mode == 'grayscale':
            self.colorize_mode = 'omniscan'
        else:
            self.colorize_mode = 'grayscale'

        print(f"🎨 Changement de colormap : {self.colorize_mode}")
        self._setup_colormap()
        self.update_display()

    def save_current_slice(self):
        """Sauvegarde la slice actuelle comme image"""
        import os
        filename = f"h5_slice_{self.current_slice:03d}.png"
        current_data = self.volume[self.current_slice, :, :]

        # Créer une figure temporaire pour la sauvegarde
        fig_save, ax_save = plt.subplots(figsize=(8, 6))
        ax_save.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax_save.set_title(f"H5 Slice {self.current_slice + 1}/{self.max_slice + 1} - Dataset: {self.dataset_path}")
        ax_save.axis('off')

        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig_save)

        print(f"Slice sauvegardée : {filename}")

    def show_slice_info(self):
        """Affiche des informations détaillées sur la slice actuelle"""
        current_data = self.volume[self.current_slice, :, :]

        print(f"\n=== INFORMATIONS SLICE {self.current_slice + 1} ===")
        print(f"Fichier H5 : {self.h5_path}")
        print(f"Dataset utilisé : {self.dataset_path}")
        print(f"Forme : {current_data.shape}")

        if self.is_nde_data:
            # Pour les données NDE, afficher les informations des données brutes
            raw_slice = self.volume_raw[self.current_slice, :, :]
            print(f"Type : Données NDE (amplitudes)")
            print(f"Mode colorisation : {self.colorize_mode}")
            print(f"Amplitude brute - Min/Max : {raw_slice.min():.2f} / {raw_slice.max():.2f}")
            print(f"Données normalisées - Min/Max : {current_data.min()} / {current_data.max()}")
            print(f"Plage originale complète : {self.min_value:.2f} à {self.max_value:.2f}")
        else:
            print(f"Type : Volume d'intensité standard")
            print(f"Min/Max : {current_data.min()} / {current_data.max()}")

            # Afficher quelques statistiques sur les valeurs uniques si pas trop nombreuses
            unique_values = np.unique(current_data)
            if len(unique_values) <= 20:
                counts = np.bincount(current_data.flatten())
                print(f"Valeurs uniques ({len(unique_values)}) :")
                for val in unique_values[:10]:  # Limiter à 10 pour l'affichage
                    count = counts[val] if val < len(counts) else 0
                    percentage = (count / current_data.size) * 100
                    print(f"  Valeur {val} : {count} pixels ({percentage:.1f}%)")
                if len(unique_values) > 10:
                    print(f"  ... et {len(unique_values) - 10} autres valeurs")
            else:
                print(f"Nombre de valeurs uniques : {len(unique_values)}")

        print("=" * 50)

    def __del__(self):
        """Ferme le fichier H5 lors de la destruction de l'objet"""
        if hasattr(self, 'h5_file'):
            self.h5_file.close()


# === UTILISATION ===
if __name__ == "__main__":
    # Chemin vers le fichier H5
    path_to_h5 = r"C:\path\to\your\file.h5"

    # Options de colorisation :
    # 'auto' : Détection automatique (OmniScan pour NDE, grayscale pour autres)
    # 'grayscale' : Toujours en niveaux de gris
    # 'omniscan' : Toujours avec les couleurs OmniScan (si fichier disponible)
    colorize_mode = 'auto'

    # Chemin vers le fichier de colormap OmniScan (optionnel)
    omniscan_colormap_path = './OmniScanColorMap.npy'

    # Lancement du visualiseur
    viewer = H5VolumeViewer(path_to_h5, colorize_mode, omniscan_colormap_path)
