#!/usr/bin/env python3
"""
Script pour parcourir récursivement un dossier et convertir chaque sous-dossier contenant des PNGs 
en un volume 3D NIfTI (.nii.gz) nommé d'après le sous-dossier.
"""
import os
import numpy as np
from PIL import Image
import argparse
import sys
from pathlib import Path

class RecursivePNGToVolumeConverter:
    def __init__(self, root_folder, output_folder=None, color_mode='L', axis_order='ZYX', skip_existing=True):
        """
        Initialise le convertisseur PNG vers volume récursif

        Args:
            root_folder: Dossier racine à parcourir récursivement
            output_folder: Dossier de sortie pour les fichiers .nii.gz (optionnel, par défaut dans chaque sous-dossier)
            color_mode: Mode couleur ('L' pour grayscale, 'RGB' pour couleur)
            axis_order: Ordre des axes ('ZYX', 'XYZ', etc.)
            skip_existing: Si True, ignore les dossiers qui ont déjà un fichier .nii.gz
        """
        self.root_folder = Path(root_folder)
        self.output_folder = Path(output_folder) if output_folder else None
        self.color_mode = color_mode
        self.axis_order = axis_order.upper()
        self.skip_existing = skip_existing

        # Vérifications
        if not self.root_folder.exists():
            raise FileNotFoundError(f"Le dossier {root_folder} n'existe pas")

        if self.output_folder and not self.output_folder.exists():
            self.output_folder.mkdir(parents=True, exist_ok=True)
            print(f"📁 Dossier de sortie créé : {self.output_folder}")

        if self.color_mode not in ['L', 'RGB']:
            raise ValueError("Mode couleur doit être 'L' (grayscale) ou 'RGB'")

    def convert_all(self):
        """Parcourt récursivement et convertit tous les dossiers contenant des PNGs"""
        print(f"🚀 Conversion récursive PNG vers volumes NIfTI")
        print(f"📂 Dossier racine : {self.root_folder}")
        if self.output_folder:
            print(f"📁 Dossier de sortie : {self.output_folder}")
        else:
            print(f"📁 Sortie : Dans chaque sous-dossier")
        print(f"🖼️ Mode couleur : {self.color_mode}")
        print(f"📊 Ordre des axes : {self.axis_order}")
        print(f"⏭️ Ignorer existants : {self.skip_existing}")
        print("-" * 60)

        # Trouver tous les dossiers contenant des PNGs
        png_folders = self.find_png_folders()
        
        if not png_folders:
            print("❌ Aucun dossier contenant des PNGs trouvé")
            return False

        print(f"📁 {len(png_folders)} dossier(s) contenant des PNGs trouvé(s)")
        print("-" * 60)

        success_count = 0
        error_count = 0

        for i, png_folder in enumerate(png_folders, 1):
            print(f"\n[{i}/{len(png_folders)}] Traitement : {png_folder.relative_to(self.root_folder)}")
            
            try:
                if self.convert_single_folder(png_folder):
                    success_count += 1
                    print(f"✅ Succès")
                else:
                    print(f"⏭️ Ignoré (fichier existant)")
            except Exception as e:
                error_count += 1
                print(f"❌ Erreur : {e}")

        print("\n" + "=" * 60)
        print(f"📊 Résumé :")
        print(f"   ✅ Succès : {success_count}")
        print(f"   ⏭️ Ignorés : {len(png_folders) - success_count - error_count}")
        print(f"   ❌ Erreurs : {error_count}")
        
        return error_count == 0

    def find_png_folders(self):
        """Trouve tous les dossiers contenant des fichiers PNG"""
        png_folders = []
        
        for root, _, _ in os.walk(self.root_folder):
            root_path = Path(root)
            png_files = list(root_path.glob('*.png'))
            
            if png_files:
                png_folders.append(root_path)
        
        return sorted(png_folders)

    def convert_single_folder(self, png_folder):
        """Convertit un seul dossier de PNGs en volume NIfTI"""
        # Déterminer le fichier de sortie
        if self.output_folder:
            # Créer un nom unique basé sur le chemin relatif
            relative_path = png_folder.relative_to(self.root_folder)
            # Remplacer les séparateurs par des underscores pour le nom de fichier
            safe_name = str(relative_path).replace(os.sep, '_')
            if not safe_name:  # Si c'est le dossier racine
                safe_name = png_folder.name
            output_file = self.output_folder / f"{safe_name}.nii.gz"
        else:
            # Nom du fichier de sortie basé sur le nom du dossier (mode original)
            output_file = png_folder / f"{png_folder.name}.nii.gz"

        # Vérifier si le fichier existe déjà
        if self.skip_existing and output_file.exists():
            return False

        # Charger les PNGs
        volume = self.load_png_stack(png_folder)

        # Réorganiser les axes si nécessaire
        volume = self.reorder_axes(volume)

        # Sauvegarder en NIfTI
        self.save_nifti(volume, output_file)

        return True

    def load_png_stack(self, png_folder):
        """Charge tous les PNGs d'un dossier, triés, et les empile en un volume 3D."""
        png_files = sorted(png_folder.glob('*.png'))
        
        if not png_files:
            raise FileNotFoundError(f"Aucun PNG trouvé dans {png_folder}")

        print(f"   📄 {len(png_files)} fichiers PNG")

        slices = []
        for png_file in png_files:
            img = Image.open(png_file).convert(self.color_mode)
            slices.append(np.array(img))

        volume = np.stack(slices, axis=0)
        print(f"   📊 Volume : {volume.shape} ({self.axis_order})")
        print(f"   💾 Taille : {volume.nbytes / (1024*1024):.2f} MB")

        return volume

    def reorder_axes(self, volume):
        """Réorganise les axes selon l'ordre spécifié"""
        if self.axis_order == 'ZYX':
            return volume  # Ordre par défaut
        elif self.axis_order == 'XYZ':
            return np.transpose(volume, (2, 1, 0))
        elif self.axis_order == 'YXZ':
            return np.transpose(volume, (1, 2, 0))
        else:
            print(f"   ⚠️ Ordre d'axes '{self.axis_order}' non supporté, utilisation de ZYX")
            return volume

    def save_nifti(self, volume, output_file):
        """Sauvegarde le volume au format NIfTI"""
        try:
            import nibabel as nib
        except ImportError:
            raise ImportError("nibabel n'est pas installé. Installez-le avec : pip install nibabel")

        nifti_img = nib.Nifti1Image(volume, affine=np.eye(4))
        nib.save(nifti_img, output_file)
        print(f"   💾 Sauvegardé : {output_file.name}")

def main():
    """Fonction principale avec arguments en ligne de commande"""
    parser = argparse.ArgumentParser(
        description="Parcourt récursivement un dossier et convertit chaque sous-dossier contenant des PNGs en volume NIfTI"
    )
    parser.add_argument("root_folder", help="Dossier racine à parcourir récursivement")
    parser.add_argument("--output-folder", help="Dossier de sortie pour les fichiers .nii.gz (optionnel)")
    parser.add_argument("--color-mode", choices=['L', 'RGB'], default='L',
                       help="Mode couleur : L (grayscale) ou RGB (défaut: L)")
    parser.add_argument("--axis-order", choices=['ZYX', 'XYZ', 'YXZ'], default='ZYX',
                       help="Ordre des axes (défaut: ZYX)")
    parser.add_argument("--overwrite", action='store_true',
                       help="Écraser les fichiers .nii.gz existants")

    args = parser.parse_args()

    try:
        # Créer le convertisseur
        converter = RecursivePNGToVolumeConverter(
            root_folder=args.root_folder,
            output_folder=args.output_folder,
            color_mode=args.color_mode,
            axis_order=args.axis_order,
            skip_existing=not args.overwrite
        )

        # Effectuer la conversion
        success = converter.convert_all()

        if not success:
            sys.exit(1)

    except Exception as e:
        print(f"❌ Erreur lors de la conversion : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
