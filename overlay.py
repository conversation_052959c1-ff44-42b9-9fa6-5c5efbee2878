import os
import cv2
import numpy as np

# === Dossiers hardcodés ===
IMAGE_DIR = r"C:\Users\<USER>\Documents\output\imagesTr_uint8"
MASK_DIR = r"C:\Users\<USER>\Documents\output\labelsTr"
OUT_DIR = r"C:\Users\<USER>\Documents\output\overlay_visuel"

os.makedirs(OUT_DIR, exist_ok=True)

# === Script pour overlay avec couleurs exactes du masque ===

for filename in os.listdir(IMAGE_DIR):
    image_path = os.path.join(IMAGE_DIR, filename)
    mask_path = os.path.join(MASK_DIR, filename)

    if not os.path.exists(mask_path):
        print(f"[⚠️] Pas de masque pour {filename}")
        continue

    # Lire image et masque
    image = cv2.imread(image_path)
    mask_original = cv2.imread(mask_path, cv2.IMREAD_UNCHANGED)

    if image is None or mask_original is None:
        print(f"[⚠️] Fichier invalide : {filename}")
        continue

    # Créer l'overlay en utilisant directement les couleurs du masque original
    overlay = image.copy()

    # Si le masque est en couleur (RGB), l'utiliser directement
    if mask_original.ndim == 3:
        # Masque couleur - utiliser les couleurs exactes
        mask_color = mask_original
        # Créer un masque binaire pour identifier les zones non-noires (fond)
        mask_non_black = np.any(mask_color != [0, 0, 0], axis=2)

        print(f"[🔧] {filename} → masque couleur détecté, pixels non-noirs : {np.sum(mask_non_black)}")

        # Appliquer les couleurs exactes du masque sur l'image
        alpha = 0.6  # Transparence
        overlay[mask_non_black] = (alpha * mask_color[mask_non_black] + (1-alpha) * image[mask_non_black]).astype(np.uint8)

    else:
        # Masque en niveaux de gris - convertir en couleur en préservant les valeurs
        unique_vals = sorted([v for v in np.unique(mask_original) if v != 0])
        print(f"[🔧] {filename} → masque niveaux de gris, valeurs : {unique_vals}")

        for val in unique_vals:
            mask_area = (mask_original == val)
            if np.sum(mask_area) > 0:
                # Convertir la valeur de gris en couleur (garder la même intensité)
                color = [val, val, val]  # Couleur grise correspondante
                alpha = 0.6
                overlay[mask_area] = (alpha * np.array(color) + (1-alpha) * image[mask_area]).astype(np.uint8)

    # Sauvegarde
    out_path = os.path.join(OUT_DIR, filename)
    cv2.imwrite(out_path, overlay)
    print(f"[✔️] Overlay créé : {filename}")

print("✅ Tous les overlays ont été générés.")
