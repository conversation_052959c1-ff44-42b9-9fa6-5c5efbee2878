"""
Visualiseur interactif pour images PNG dans un dossier
Remplace l'ancien visualise_10_mask.py avec des contrôles manuels et détection automatique
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from matplotlib.colors import ListedColormap
from pathlib import Path

class PNGFolderViewer:
    def __init__(self, folder_path, max_images=None):
        """
        Visualiseur interactif pour un dossier d'images PNG
        
        Args:
            folder_path (str): Chemin vers le dossier contenant les PNG
            max_images (int): Nombre maximum d'images à charger (None = toutes)
        """
        self.folder_path = Path(folder_path)
        self.current_image = 0
        
        # Charger la liste des fichiers PNG
        self.image_files = sorted([f for f in os.listdir(folder_path) if f.lower().endswith('.png')])
        
        if not self.image_files:
            raise ValueError(f"Aucun fichier PNG trouvé dans {folder_path}")
        
        # Limiter le nombre d'images si spécifié
        if max_images and max_images < len(self.image_files):
            self.image_files = self.image_files[:max_images]
        
        self.max_image = len(self.image_files) - 1
        
        print(f"Dossier chargé : {folder_path}")
        print(f"Nombre d'images : {len(self.image_files)}")
        
        # Analyser le premier fichier pour détecter le type
        first_image_path = self.folder_path / self.image_files[0]
        first_image = np.array(Image.open(first_image_path).convert("L"))
        unique_values = np.unique(first_image)
        
        print(f"Valeurs uniques dans la première image : {unique_values}")
        
        # Détection automatique du type
        self.is_mask = self._detect_mask(unique_values)
        
        if self.is_mask:
            print("Détection : Images de masque/segmentation détectées")
            print("Application du colormap pour les classes 0-4")
            # Colormap pour les masques de segmentation
            self.cmap = ListedColormap([
                (0, 0, 0),        # 0: background - noir
                (0, 0, 1),        # 1: frontwall - bleu
                (0, 1, 0),        # 2: backwall - vert
                (1, 0, 0),        # 3: flaw - rouge
                (1, 1, 0)         # 4: indication - jaune
            ])
            self.vmin, self.vmax = 0, 4
        else:
            print("Détection : Images d'intensité standard détectées")
            print("Application du colormap en niveaux de gris")
            self.cmap = 'gray'
            self.vmin, self.vmax = first_image.min(), first_image.max()
        
        # Configuration de la figure
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.fig.suptitle(f"Visualiseur PNG - {self.folder_path.name}", fontsize=14)
        
        # Affichage initial
        self.update_display()
        
        # Connexion des événements
        self.fig.canvas.mpl_connect('key_press_event', self.on_key_press)
        
        plt.show()
    
    def _detect_mask(self, unique_values):
        """Détecte si les images sont des masques de segmentation"""
        # Critères similaires au visualiseur de volumes
        if not np.allclose(unique_values, unique_values.astype(int)):
            return False
        
        if unique_values.max() <= 4 and unique_values.min() >= 0:
            if len(unique_values) <= 6:
                return True
        
        return False
    
    def update_display(self):
        """Met à jour l'affichage de l'image actuelle"""
        self.ax.clear()
        
        # Charger l'image actuelle
        current_file = self.image_files[self.current_image]
        image_path = self.folder_path / current_file
        current_data = np.array(Image.open(image_path).convert("L"))
        
        # Affichage de l'image
        im = self.ax.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        
        # Titre avec informations
        title = f"Image {self.current_image + 1}/{self.max_image + 1} - {current_file}"
        if self.is_mask:
            unique_in_image = np.unique(current_data)
            title += f"\nClasses présentes: {unique_in_image}"
        else:
            title += f"\nMin: {current_data.min()}, Max: {current_data.max()}"
        
        self.ax.set_title(title, fontsize=10)
        self.ax.axis('off')
        
        # Ajouter une colorbar pour les masques
        if self.is_mask and not hasattr(self, 'colorbar'):
            self.colorbar = plt.colorbar(im, ax=self.ax, shrink=0.8)
            self.colorbar.set_label('Classes de segmentation')
            self.colorbar.set_ticks([0, 1, 2, 3, 4])
            self.colorbar.set_ticklabels(['Background', 'Frontwall', 'Backwall', 'Flaw', 'Indication'])
        
        # Instructions en bas
        instructions_text = "← → : Précédent/Suivant | PgUp/PgDn : ±10 | Home/End : Premier/Dernier | Q : Quitter | S : Sauvegarder | I : Info"
        self.fig.text(0.5, 0.02, instructions_text, ha='center', fontsize=9, style='italic')
        
        self.fig.canvas.draw()
    
    def on_key_press(self, event):
        """Gestion des événements clavier"""
        if event.key == 'right' or event.key == 'down':
            self.next_image()
        elif event.key == 'left' or event.key == 'up':
            self.previous_image()
        elif event.key == 'pagedown':
            self.jump_images(10)
        elif event.key == 'pageup':
            self.jump_images(-10)
        elif event.key == 'home':
            self.go_to_image(0)
        elif event.key == 'end':
            self.go_to_image(self.max_image)
        elif event.key == 'q' or event.key == 'escape':
            plt.close(self.fig)
        elif event.key == 's':
            self.save_current_image()
        elif event.key == 'i':
            self.show_image_info()
    
    def next_image(self):
        """Passe à l'image suivante"""
        if self.current_image < self.max_image:
            self.current_image += 1
            self.update_display()
        else:
            print("Déjà à la dernière image")
    
    def previous_image(self):
        """Passe à l'image précédente"""
        if self.current_image > 0:
            self.current_image -= 1
            self.update_display()
        else:
            print("Déjà à la première image")
    
    def jump_images(self, n):
        """Saute de n images"""
        new_image = self.current_image + n
        new_image = max(0, min(new_image, self.max_image))
        if new_image != self.current_image:
            self.current_image = new_image
            self.update_display()
    
    def go_to_image(self, image_num):
        """Va directement à une image spécifique"""
        image_num = max(0, min(image_num, self.max_image))
        if image_num != self.current_image:
            self.current_image = image_num
            self.update_display()
    
    def save_current_image(self):
        """Sauvegarde l'image actuelle avec colormap appliquée"""
        current_file = self.image_files[self.current_image]
        image_path = self.folder_path / current_file
        current_data = np.array(Image.open(image_path).convert("L"))
        
        # Nom de fichier pour la sauvegarde
        base_name = Path(current_file).stem
        filename = f"colored_{base_name}.png"
        
        # Créer une figure temporaire pour la sauvegarde
        fig_save, ax_save = plt.subplots(figsize=(10, 8))
        ax_save.imshow(current_data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax_save.set_title(f"Image {self.current_image + 1}/{self.max_image + 1} - {current_file}")
        ax_save.axis('off')
        
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig_save)
        
        print(f"Image sauvegardée : {filename}")
    
    def show_image_info(self):
        """Affiche des informations détaillées sur l'image actuelle"""
        current_file = self.image_files[self.current_image]
        image_path = self.folder_path / current_file
        current_data = np.array(Image.open(image_path).convert("L"))
        unique_values, counts = np.unique(current_data, return_counts=True)
        
        print(f"\n=== INFORMATIONS IMAGE {self.current_image + 1} ===")
        print(f"Fichier : {current_file}")
        print(f"Forme : {current_data.shape}")
        
        type_text = "Masque de segmentation" if self.is_mask else "Image d'intensité"
        print(f"Type : {type_text}")
        
        print(f"Min/Max : {current_data.min()} / {current_data.max()}")
        print(f"Valeurs uniques et leurs occurrences :")
        for val, count in zip(unique_values, counts):
            percentage = (count / current_data.size) * 100
            if self.is_mask:
                class_names = {0: 'Background', 1: 'Frontwall', 2: 'Backwall', 3: 'Flaw', 4: 'Indication'}
                class_name = class_names.get(int(val), f'Classe {int(val)}')
                print(f"  {class_name} ({int(val)}) : {count} pixels ({percentage:.1f}%)")
            else:
                print(f"  Valeur {val} : {count} pixels ({percentage:.1f}%)")
        print("=" * 50)


# === UTILISATION ===
if __name__ == "__main__":
    # Chemin vers le dossier d'images
    MASKS_DIR = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_uint8"
    
    # Lancement du visualiseur (None = toutes les images, ou spécifier un nombre)
    try:
        viewer = PNGFolderViewer(MASKS_DIR, max_images=None)
    except Exception as e:
        print(f"Erreur : {e}")
        print("Vérifiez que le dossier existe et contient des fichiers PNG")
