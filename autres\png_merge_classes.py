"""
Script de fusion des classes dans les images PNG
Permet de fusionner plusieurs classes en une seule classe cible
Auteur: Gabriel Forest
Date: 2025-07-14
"""

import os
import numpy as np
from PIL import Image
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import json

class PNGClassMerger:
    def __init__(self):
        """Initialise le fusionneur de classes PNG"""
        self.input_folder = ""
        self.output_folder = ""
        self.merge_threshold = 1
        self.processed_count = 0
        self.total_count = 0
        
    def analyze_folder(self, folder_path):
        """
        Analyse un dossier et ses sous-dossiers récursivement pour détecter toutes les classes présentes

        Args:
            folder_path (str): Chemin vers le dossier à analyser

        Returns:
            set: Ensemble de toutes les classes trouvées
        """
        all_classes = set()

        # Parcourir récursivement tous les fichiers PNG
        for root, dirs, files in os.walk(folder_path):
            png_files = [f for f in files if f.lower().endswith('.png')]

            for filename in png_files:
                file_path = os.path.join(root, filename)
                try:
                    image = np.array(Image.open(file_path).convert("L"))
                    unique_values = np.unique(image)
                    all_classes.update(unique_values)
                except Exception as e:
                    print(f"Erreur lors de l'analyse de {file_path}: {e}")

        return sorted(all_classes)
    
    def merge_classes(self, image_array, threshold):
        """
        Fusionne les classes selon le seuil spécifié
        
        Args:
            image_array (np.array): Image à traiter
            threshold (int): Seuil de fusion (classes >= threshold deviennent 1)
            
        Returns:
            np.array: Image avec classes fusionnées
        """
        result = image_array.copy()
        
        # Toutes les classes >= threshold deviennent 1
        result[image_array >= threshold] = 1
        
        return result
    
    def process_folder(self, input_folder, output_folder, threshold, progress_callback=None):
        """
        Traite récursivement tous les fichiers PNG d'un dossier et de ses sous-dossiers

        Args:
            input_folder (str): Dossier source
            output_folder (str): Dossier de destination
            threshold (int): Seuil de fusion
            progress_callback (function): Fonction de callback pour le progrès
        """
        # Créer le dossier de sortie s'il n'existe pas
        os.makedirs(output_folder, exist_ok=True)

        # Collecter tous les fichiers PNG récursivement
        all_png_files = []
        for root, dirs, files in os.walk(input_folder):
            png_files = [f for f in files if f.lower().endswith('.png')]
            for filename in png_files:
                input_path = os.path.join(root, filename)
                # Calculer le chemin relatif pour préserver la structure
                rel_path = os.path.relpath(input_path, input_folder)
                all_png_files.append((input_path, rel_path))

        self.total_count = len(all_png_files)
        self.processed_count = 0

        if self.total_count == 0:
            raise ValueError("Aucun fichier PNG trouvé dans le dossier source et ses sous-dossiers")

        print(f"Traitement récursif de {self.total_count} fichiers PNG...")
        print(f"Seuil de fusion: classes >= {threshold} → classe 1")

        for input_path, rel_path in all_png_files:
            # Créer le chemin de sortie en préservant la structure des dossiers
            output_path = os.path.join(output_folder, rel_path)
            output_dir = os.path.dirname(output_path)

            # Créer le dossier de destination si nécessaire
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            try:
                # Charger l'image
                image = np.array(Image.open(input_path).convert("L"))

                # Analyser les classes avant traitement
                classes_before = np.unique(image)

                # Fusionner les classes
                merged_image = self.merge_classes(image, threshold)

                # Analyser les classes après traitement
                classes_after = np.unique(merged_image)

                # Sauvegarder l'image traitée
                Image.fromarray(merged_image.astype(np.uint8)).save(output_path)

                self.processed_count += 1

                print(f"✓ {rel_path}: {classes_before} → {classes_after}")

                # Callback de progrès
                if progress_callback:
                    progress_callback(self.processed_count, self.total_count, rel_path)

            except Exception as e:
                print(f"✗ Erreur avec {rel_path}: {e}")

        print(f"\nTraitement terminé: {self.processed_count}/{self.total_count} fichiers traités")


class PNGClassMergerGUI:
    def __init__(self):
        """Interface graphique pour le fusionneur de classes PNG"""
        self.merger = PNGClassMerger()
        self.setup_gui()
        
    def setup_gui(self):
        """Configure l'interface graphique"""
        self.root = tk.Tk()
        self.root.title("Fusionneur de Classes PNG")
        self.root.geometry("800x600")
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(main_frame, text="🔄 Fusionneur de Classes PNG", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Description
        desc_text = """Ce script permet de fusionner les classes dans des images PNG de manière récursive.
Toutes les classes supérieures ou égales au seuil spécifié seront fusionnées en classe 1.
La classe 0 (background) reste inchangée.
La structure des dossiers et sous-dossiers sera préservée dans la sortie."""
        
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.LEFT)
        desc_label.pack(pady=(0, 20))
        
        # Sélection du dossier d'entrée
        input_frame = ttk.LabelFrame(main_frame, text="📁 Dossier d'entrée", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.input_path_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_path_var, width=60).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(input_frame, text="Parcourir", command=self.select_input_folder).pack(side=tk.LEFT)
        
        # Sélection du dossier de sortie
        output_frame = ttk.LabelFrame(main_frame, text="📁 Dossier de sortie", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.output_path_var = tk.StringVar()
        ttk.Entry(output_frame, textvariable=self.output_path_var, width=60).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(output_frame, text="Parcourir", command=self.select_output_folder).pack(side=tk.LEFT)
        
        # Configuration du seuil
        threshold_frame = ttk.LabelFrame(main_frame, text="⚙️ Configuration", padding="10")
        threshold_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(threshold_frame, text="Seuil de fusion (classes >= seuil → classe 1):").pack(anchor=tk.W)
        
        threshold_control_frame = ttk.Frame(threshold_frame)
        threshold_control_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.threshold_var = tk.IntVar(value=1)
        threshold_spinbox = ttk.Spinbox(threshold_control_frame, from_=1, to=255, 
                                       textvariable=self.threshold_var, width=10)
        threshold_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(threshold_control_frame, text="Analyser dossier", 
                  command=self.analyze_input_folder).pack(side=tk.LEFT)
        
        # Zone d'analyse
        self.analysis_frame = ttk.LabelFrame(main_frame, text="📊 Analyse du dossier", padding="10")
        self.analysis_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.analysis_text = tk.Text(self.analysis_frame, height=6, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(self.analysis_frame, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=scrollbar.set)
        
        self.analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Boutons d'action
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.process_button = ttk.Button(action_frame, text="🚀 Traiter les images", 
                                        command=self.start_processing)
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(action_frame, text="❌ Quitter", command=self.root.quit).pack(side=tk.RIGHT)
        
        # Barre de progression
        self.progress_frame = ttk.LabelFrame(main_frame, text="📈 Progression", padding="10")
        self.progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.progress_label = ttk.Label(self.progress_frame, text="Prêt à traiter")
        self.progress_label.pack()
        
    def select_input_folder(self):
        """Sélectionne le dossier d'entrée"""
        folder = filedialog.askdirectory(title="Sélectionner le dossier d'entrée")
        if folder:
            self.input_path_var.set(folder)
            
    def select_output_folder(self):
        """Sélectionne le dossier de sortie"""
        folder = filedialog.askdirectory(title="Sélectionner le dossier de sortie")
        if folder:
            self.output_path_var.set(folder)
            
    def analyze_input_folder(self):
        """Analyse le dossier d'entrée et ses sous-dossiers pour détecter les classes"""
        input_folder = self.input_path_var.get()

        if not input_folder or not os.path.exists(input_folder):
            messagebox.showerror("Erreur", "Veuillez sélectionner un dossier d'entrée valide")
            return

        try:
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, "Analyse récursive en cours...\n")
            self.root.update()

            # Compter les fichiers PNG récursivement
            png_count = 0
            folder_count = 0
            for root, dirs, files in os.walk(input_folder):
                png_files = [f for f in files if f.lower().endswith('.png')]
                if png_files:
                    folder_count += 1
                    png_count += len(png_files)

            classes = self.merger.analyze_folder(input_folder)

            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, f"Analyse récursive terminée:\n")
            self.analysis_text.insert(tk.END, f"• {png_count} fichiers PNG trouvés\n")
            self.analysis_text.insert(tk.END, f"• {folder_count} dossiers contenant des PNG\n")
            self.analysis_text.insert(tk.END, f"• Classes détectées: {classes}\n\n")

            threshold = self.threshold_var.get()
            classes_to_merge = [c for c in classes if c >= threshold]
            classes_to_keep = [c for c in classes if c < threshold]

            self.analysis_text.insert(tk.END, f"Avec le seuil {threshold}:\n")
            self.analysis_text.insert(tk.END, f"• Classes conservées: {classes_to_keep}\n")
            self.analysis_text.insert(tk.END, f"• Classes fusionnées en classe 1: {classes_to_merge}\n\n")

            if classes_to_merge:
                self.analysis_text.insert(tk.END, "Résultat final: classes 0 et 1 uniquement\n")
                self.analysis_text.insert(tk.END, "La structure des dossiers sera préservée dans la sortie.\n")
            else:
                self.analysis_text.insert(tk.END, "Aucune classe ne sera fusionnée avec ce seuil\n")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'analyse: {e}")

    def progress_callback(self, current, total, filename):
        """Callback pour mettre à jour la progression"""
        progress = (current / total) * 100
        self.progress_var.set(progress)
        self.progress_label.config(text=f"Traitement: {current}/{total} - {filename}")
        self.root.update()

    def start_processing(self):
        """Lance le traitement en arrière-plan"""
        input_folder = self.input_path_var.get()
        output_folder = self.output_path_var.get()
        threshold = self.threshold_var.get()

        if not input_folder or not os.path.exists(input_folder):
            messagebox.showerror("Erreur", "Veuillez sélectionner un dossier d'entrée valide")
            return

        if not output_folder:
            messagebox.showerror("Erreur", "Veuillez sélectionner un dossier de sortie")
            return

        # Confirmation si le dossier de sortie existe et n'est pas vide
        if os.path.exists(output_folder) and os.listdir(output_folder):
            if not messagebox.askyesno("Confirmation",
                                     "Le dossier de sortie n'est pas vide. Continuer?"):
                return

        # Désactiver le bouton pendant le traitement
        self.process_button.config(state='disabled')

        # Lancer le traitement dans un thread séparé
        thread = threading.Thread(target=self.process_images,
                                 args=(input_folder, output_folder, threshold))
        thread.daemon = True
        thread.start()

    def process_images(self, input_folder, output_folder, threshold):
        """Traite les images (exécuté dans un thread séparé)"""
        try:
            self.merger.process_folder(input_folder, output_folder, threshold,
                                     self.progress_callback)

            # Réactiver le bouton et afficher le succès
            self.root.after(0, lambda: self.process_button.config(state='normal'))
            self.root.after(0, lambda: messagebox.showinfo("Succès",
                           f"Traitement terminé!\n{self.merger.processed_count} fichiers traités"))

        except Exception as e:
            self.root.after(0, lambda: self.process_button.config(state='normal'))
            self.root.after(0, lambda: messagebox.showerror("Erreur", f"Erreur: {e}"))

    def run(self):
        """Lance l'interface graphique"""
        self.root.mainloop()


# === UTILISATION ===
def main():
    """Fonction principale avec support ligne de commande et GUI"""
    import sys

    if len(sys.argv) > 1:
        # Mode ligne de commande
        if len(sys.argv) != 4:
            print("Usage: python png_merge_classes.py <dossier_entree> <dossier_sortie> <seuil>")
            print("Exemple: python png_merge_classes.py ./input ./output 1")
            sys.exit(1)

        input_folder = sys.argv[1]
        output_folder = sys.argv[2]
        threshold = int(sys.argv[3])

        if not os.path.exists(input_folder):
            print(f"Erreur: Le dossier d'entrée '{input_folder}' n'existe pas")
            sys.exit(1)

        # Traitement en ligne de commande
        merger = PNGClassMerger()

        print("=== FUSIONNEUR DE CLASSES PNG ===")
        print(f"Dossier d'entrée: {input_folder}")
        print(f"Dossier de sortie: {output_folder}")
        print(f"Seuil de fusion: {threshold}")
        print()

        # Analyse préalable
        print("Analyse récursive du dossier...")

        # Compter les fichiers PNG récursivement
        png_count = 0
        folder_count = 0
        for root, dirs, files in os.walk(input_folder):
            png_files = [f for f in files if f.lower().endswith('.png')]
            if png_files:
                folder_count += 1
                png_count += len(png_files)

        classes = merger.analyze_folder(input_folder)
        print(f"Fichiers PNG trouvés: {png_count} dans {folder_count} dossiers")
        print(f"Classes détectées: {classes}")

        classes_to_merge = [c for c in classes if c >= threshold]
        classes_to_keep = [c for c in classes if c < threshold]

        print(f"Classes conservées: {classes_to_keep}")
        print(f"Classes fusionnées en classe 1: {classes_to_merge}")
        print("La structure des dossiers sera préservée dans la sortie.")
        print()

        # Confirmation
        response = input("Continuer le traitement? (o/N): ")
        if response.lower() not in ['o', 'oui', 'y', 'yes']:
            print("Traitement annulé")
            sys.exit(0)

        # Traitement
        try:
            merger.process_folder(input_folder, output_folder, threshold)
            print(f"\n✅ Traitement terminé avec succès!")
        except Exception as e:
            print(f"\n❌ Erreur: {e}")
            sys.exit(1)
    else:
        # Mode interface graphique
        app = PNGClassMergerGUI()
        app.run()

if __name__ == "__main__":
    main()
