#!/usr/bin/env python3
"""
Script de test pour vérifier que la correction de view_mask.py fonctionne
"""

import os
import sys
import tempfile

def test_view_mask_script_creation():
    """Test la création du script temporaire pour view_mask.py"""
    
    # Simuler la fonction de l'interface graphique
    def _create_temp_visualize_mask_script(file_path):
        """Version de test de la fonction corrigée"""
        temp_script = "temp_test_visualize_mask.py"
        with open("view_mask.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('→', '->')

        # Méthode plus robuste : remplacer toute ligne qui définit image_path
        import re
        # Pattern pour capturer n'importe quelle définition d'image_path
        pattern = r'image_path\s*=\s*r?"[^"]*"'
        replacement = f'image_path = r"{file_path}"'
        content = re.sub(pattern, replacement, content)
        
        # Si aucun remplacement n'a été fait, chercher d'autres patterns
        if 'image_path = r"' not in content or file_path not in content:
            # Pattern alternatif pour les chemins avec des backslashes
            pattern2 = r"image_path\s*=\s*r?'[^']*'"
            content = re.sub(pattern2, replacement, content)
            
            # Si toujours pas trouvé, ajouter la définition avant visualize_image
            if 'image_path = r"' not in content or file_path not in content:
                # Insérer la définition juste avant l'appel à visualize_image
                pattern3 = r'(\s*# Visualiser l\'image\s*\n\s*visualize_image\()[^)]*(\))'
                replacement3 = f'\\1r"{file_path}"\\2'
                content = re.sub(pattern3, replacement3, content)

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script
    
    # Test avec un chemin d'exemple (utiliser des slashes normaux pour éviter les problèmes)
    test_file_path = "C:/Users/<USER>/Documents/traitement_img/test_labels/test_label_000000.png"
    
    print("🧪 Test de la fonction de création du script temporaire...")
    
    try:
        # Créer le script temporaire
        temp_script = _create_temp_visualize_mask_script(test_file_path)
        
        # Vérifier que le fichier a été créé
        if os.path.exists(temp_script):
            print("✅ Script temporaire créé avec succès")
            
            # Lire le contenu et vérifier que le chemin a été remplacé
            with open(temp_script, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if test_file_path in content:
                print("✅ Chemin correctement remplacé dans le script")
                print(f"   Chemin utilisé: {test_file_path}")
            else:
                print("❌ Le chemin n'a pas été correctement remplacé")
                print("Contenu du script:")
                print(content[:500] + "..." if len(content) > 500 else content)
            
            # Nettoyer
            os.remove(temp_script)
            print("🧹 Fichier temporaire nettoyé")
            
        else:
            print("❌ Échec de la création du script temporaire")
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    print("🔧 Test des corrections pour view_mask.py")
    print("=" * 50)
    test_view_mask_script_creation()
    print("=" * 50)
    print("✅ Test terminé")
