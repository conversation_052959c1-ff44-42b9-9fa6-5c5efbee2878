import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import os
from pathlib import Path
import argparse

class NPZToPNGExporter:
    def __init__(self, npz_path, output_dir, data_key=None, prefix="slice", image_type="auto", export_uint8=False, rotation=0):
        """
        Exporte toutes les slices d'un volume NPZ en images PNG

        Args:
            npz_path: Chemin vers le fichier NPZ
            output_dir: Dossier de sortie pour les images PNG
            data_key: Clé spécifique à utiliser dans le NPZ (None pour sélection automatique)
            prefix: Préfixe pour les noms de fichiers (défaut: "slice")
            image_type: Type d'images ("auto", "imagesTr", "labelsTr")
            export_uint8: Si True, export en uint8, sinon RGB pour imagesTr ou couleurs distinctes pour labelsTr
            rotation: Rotation à appliquer en degrés (0, 90, 180, 270)
        """
        self.npz_path = npz_path
        self.output_dir = Path(output_dir)
        self.prefix = prefix
        self.image_type = image_type
        self.export_uint8 = export_uint8
        self.rotation = rotation
        
        # Créer le dossier de sortie s'il n'existe pas
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Charger le fichier NPZ
        print(f"📂 Chargement du fichier NPZ : {npz_path}")
        self.npz_data = np.load(npz_path)
        print(f"📊 Clés disponibles : {list(self.npz_data.keys())}")
        
        # Sélectionner la clé de données
        self.data_key = self._select_data_key(data_key)
        print(f"🔑 Utilisation de la clé : '{self.data_key}'")
        
        # Charger les données du volume
        self.volume_raw = self.npz_data[self.data_key]

        # Assurer que le volume a au moins 3 dimensions
        if self.volume_raw.ndim == 2:
            self.volume_raw = self.volume_raw[np.newaxis, :, :]
        elif self.volume_raw.ndim > 3:
            print(f"⚠️ Volume avec {self.volume_raw.ndim} dimensions détecté. Utilisation des 3 premières dimensions.")
            self.volume_raw = self.volume_raw[:, :, :] if self.volume_raw.ndim == 4 else self.volume_raw

        # Déterminer les dimensions et résolution par défaut
        self.original_shape = self.volume_raw.shape
        print(f"📊 Volume chargé : {self.original_shape} (Z, Y, X)")
        print(f"📈 Valeurs uniques : {np.unique(self.volume_raw)}")

        # Détection automatique du type d'images
        unique_values = np.unique(self.volume_raw)
        self.detected_type = self._detect_image_type(unique_values)

        # Utiliser le type spécifié ou celui détecté
        if self.image_type == "auto":
            self.final_image_type = self.detected_type
        else:
            self.final_image_type = self.image_type

        print(f"🏷️ Type d'images : {self.final_image_type}")
        print(f"🎨 Mode export : {'uint8' if self.export_uint8 else 'RGB/Couleurs'}")

        # Préparer les données selon le type et le mode d'export
        self._prepare_data()

        # Configuration du colormap
        self._setup_colormap()
    
    def _select_data_key(self, data_key):
        """Sélectionne automatiquement la meilleure clé de données à utiliser"""
        available_keys = list(self.npz_data.keys())

        if data_key is not None:
            if data_key in available_keys:
                return data_key
            else:
                print(f"⚠️ Clé '{data_key}' non trouvée. Clés disponibles : {available_keys}")

        # Sélection automatique
        if len(available_keys) == 1:
            return available_keys[0]

        # Plusieurs clés disponibles - sélection automatique intelligente
        print("Plusieurs clés disponibles. Sélection automatique de la meilleure clé :")

        # Analyser chaque clé pour trouver la meilleure
        candidates = []
        for key in available_keys:
            data = self.npz_data[key]
            shape = data.shape
            dtype = data.dtype

            print(f"  📄 '{key}' - Shape: {shape}, Type: {dtype}")

            # Critères de sélection (par ordre de priorité) :
            # 1. Doit être 3D (Z, Y, X)
            # 2. Préférer uint8
            # 3. Éviter les types booléens
            # 4. Éviter les tableaux 1D

            score = 0

            if len(shape) == 3:
                score += 100  # Priorité maximale pour 3D

                if dtype == 'uint8':
                    score += 50  # Bonus pour uint8
                elif dtype in ['uint16', 'int16', 'uint32', 'int32']:
                    score += 30  # Bonus moyen pour autres entiers
                elif dtype in ['float32', 'float64']:
                    score += 10  # Bonus faible pour float

                if dtype != 'bool':
                    score += 20  # Bonus pour non-booléen

            elif len(shape) == 2:
                score += 50  # Acceptable pour 2D
            elif len(shape) == 1:
                score += 1   # Très faible priorité pour 1D

            candidates.append((key, score, shape, dtype))

        # Trier par score décroissant
        candidates.sort(key=lambda x: x[1], reverse=True)

        if candidates and candidates[0][1] > 0:
            selected_key = candidates[0][0]
            print(f"🎯 Sélection automatique de la meilleure clé : '{selected_key}'")
            print(f"   Shape: {candidates[0][2]}, Type: {candidates[0][3]}")
            return selected_key
        else:
            print("⚠️ Aucune clé appropriée trouvée (pas de données 3D)")
            raise ValueError("Aucune clé appropriée trouvée dans le fichier NPZ")
    
    def _detect_image_type(self, unique_values):
        """Détecte si le volume contient des images (imagesTr) ou des labels (labelsTr)"""
        # Critères pour détecter des labels :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans une plage limitée (typiquement 0-4 pour les classes)
        # 3. Nombre limité de valeurs uniques

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return "imagesTr"  # Valeurs continues -> images

        # Vérifier si les valeurs correspondent à des classes de segmentation
        if unique_values.max() <= 10 and unique_values.min() >= 0:
            if len(unique_values) <= 15:  # Nombre raisonnable de classes
                return "labelsTr"

        return "imagesTr"  # Par défaut, considérer comme des images

    def _prepare_data(self):
        """Prépare les données selon le type d'images et le mode d'export"""
        if self.final_image_type == "labelsTr":
            # Pour les labels, garder les valeurs exactes
            self.volume = self.volume_raw.astype(np.uint8)
        else:
            # Pour les images, normaliser vers 0-255
            if self.volume_raw.dtype in [np.float32, np.float64]:
                # Normaliser les données float
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > vmin:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = np.zeros_like(self.volume_raw, dtype=np.uint8)
            else:
                # Données entières, normaliser selon la plage
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > 255:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = self.volume_raw.astype(np.uint8)

    def _setup_colormap(self):
        """Configure le colormap selon le type d'images et le mode d'export"""
        if self.final_image_type == "labelsTr":
            if self.export_uint8:
                print("🎨 Labels : Export direct des classes (uint8)")
                self.cmap = None  # Pas de colormap, export direct
            else:
                print("🎨 Labels : Export avec couleurs distinctes pour visualisation")
                # Colormap avec couleurs distinctes pour les classes
                self.cmap = ListedColormap([
                    (0, 0, 0),        # 0: background - noir
                    (0, 0, 1),        # 1: classe 1 - bleu
                    (0, 1, 0),        # 2: classe 2 - vert
                    (1, 0, 0),        # 3: classe 3 - rouge
                    (1, 1, 0),        # 4: classe 4 - jaune
                    (1, 0, 1),        # 5: classe 5 - magenta
                    (0, 1, 1),        # 6: classe 6 - cyan
                    (0.5, 0.5, 0.5),  # 7: classe 7 - gris
                    (1, 0.5, 0),      # 8: classe 8 - orange
                    (0.5, 0, 0.5),    # 9: classe 9 - violet
                ])
        else:  # imagesTr
            if self.export_uint8:
                print("🎨 Images : Export en niveaux de gris (uint8)")
                self.cmap = 'gray'
            else:
                print("🎨 Images : Export en RGB avec colormap")
                self.cmap = 'viridis'  # Colormap par défaut pour les images

        # Définir les limites de valeurs
        self.vmin, self.vmax = 0, 255
    
    def export_all_slices(self, dpi=150, figsize=(8, 6)):
        """
        Exporte toutes les slices en images PNG
        
        Args:
            dpi: Résolution des images (défaut: 150)
            figsize: Taille de la figure (défaut: (8, 6))
        """
        num_slices = self.volume.shape[0]
        print(f"🚀 Début de l'export de {num_slices} slices...")
        
        for slice_idx in range(num_slices):
            self._export_slice(slice_idx, dpi, figsize)
            
            # Afficher le progrès
            if (slice_idx + 1) % 10 == 0 or slice_idx == num_slices - 1:
                progress = ((slice_idx + 1) / num_slices) * 100
                print(f"📈 Progrès : {slice_idx + 1}/{num_slices} ({progress:.1f}%)")
        
        print(f"✅ Export terminé ! {num_slices} images sauvegardées dans : {self.output_dir}")
        
    def _apply_rotation(self, data):
        """Applique la rotation spécifiée aux données (sens horaire)"""
        if self.rotation == 0:
            return data
        elif self.rotation == 90:
            return np.rot90(data, k=-1)  # Rotation horaire de 90°
        elif self.rotation == 180:
            return np.rot90(data, k=2)   # Rotation de 180°
        elif self.rotation == 270:
            return np.rot90(data, k=1)   # Rotation horaire de 270° = anti-horaire de 90°
        else:
            return data

    def _export_slice(self, slice_idx, dpi, figsize):
        """Exporte une slice spécifique selon le mode choisi"""
        current_data = self.volume[slice_idx, :, :]

        # Appliquer la rotation si nécessaire
        current_data = self._apply_rotation(current_data)

        # Créer le nom de fichier avec sérialisation simple à 4 chiffres
        filename = f"{slice_idx + 1:04d}.png"
        filepath = self.output_dir / filename

        if self.final_image_type == "labelsTr" and self.export_uint8:
            # Export direct des classes sans colormap
            from PIL import Image
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)
        elif not self.export_uint8 and self.cmap is not None:
            # Export RGB avec colormap
            if hasattr(self.cmap, '__call__'):
                # Colormap matplotlib
                if self.final_image_type == "labelsTr":
                    # Pour les labels, normaliser selon le nombre de classes
                    max_class = current_data.max()
                    if max_class > 0:
                        normalized_data = current_data / max_class
                    else:
                        normalized_data = current_data
                else:
                    # Pour les images, normaliser vers 0-1
                    normalized_data = current_data / 255.0

                colored_data = self.cmap(normalized_data)
                rgb_data = (colored_data[:, :, :3] * 255).astype(np.uint8)
                from PIL import Image
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
            else:
                # Fallback : convertir en RGB grayscale
                from PIL import Image
                rgb_data = np.stack([current_data] * 3, axis=-1)
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
        else:
            # Export uint8 en grayscale
            from PIL import Image
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)

    def _export_with_matplotlib(self, data, filepath, dpi, figsize):
        """Export avec matplotlib (fallback)"""
        # Calculer figsize basé sur les dimensions réelles des données
        height, width = data.shape
        # Utiliser les dimensions réelles en pouces pour préserver les proportions
        fig_width = width / dpi
        fig_height = height / dpi

        fig, ax = plt.subplots(figsize=(fig_width, fig_height))
        ax.imshow(data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        plt.savefig(filepath, dpi=dpi, bbox_inches='tight', pad_inches=0, facecolor='white')
        plt.close(fig)
    
    def export_slice_range(self, start_slice, end_slice, dpi=150, figsize=(8, 6)):
        """
        Exporte une plage de slices
        
        Args:
            start_slice: Index de début (inclus)
            end_slice: Index de fin (inclus)
            dpi: Résolution des images
            figsize: Taille de la figure
        """
        num_slices = self.volume.shape[0]
        start_slice = max(0, start_slice)
        end_slice = min(num_slices - 1, end_slice)
        
        print(f"🚀 Export des slices {start_slice} à {end_slice}...")
        
        for slice_idx in range(start_slice, end_slice + 1):
            self._export_slice(slice_idx, dpi, figsize)
        
        exported_count = end_slice - start_slice + 1
        print(f"✅ Export terminé ! {exported_count} images sauvegardées dans : {self.output_dir}")
    
    def get_slice_info(self, slice_idx):
        """Retourne des informations sur une slice spécifique"""
        if slice_idx < 0 or slice_idx >= self.volume.shape[0]:
            return None
        
        current_data = self.volume[slice_idx, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)
        
        info = {
            'slice_index': slice_idx,
            'shape': current_data.shape,
            'min_value': current_data.min(),
            'max_value': current_data.max(),
            'unique_values': unique_values,
            'value_counts': counts,
            'is_mask': self.is_mask,
            'data_key': self.data_key
        }
        
        return info

    def list_available_keys(self):
        """Liste toutes les clés disponibles avec leurs informations"""
        print(f"\n=== CLÉS DISPONIBLES DANS {self.npz_path} ===")
        for key in self.npz_data.keys():
            data = self.npz_data[key]
            print(f"Clé: '{key}'")
            print(f"  Shape: {data.shape}")
            print(f"  Type: {data.dtype}")
            print(f"  Min/Max: {data.min()}/{data.max()}")
            print(f"  Valeurs uniques: {len(np.unique(data))}")
            print()


def main():
    """Fonction principale avec interface en ligne de commande"""
    parser = argparse.ArgumentParser(description="Exporte les slices d'un volume NPZ en images PNG")
    parser.add_argument("input_file", help="Chemin vers le fichier NPZ")
    parser.add_argument("output_dir", help="Dossier de sortie pour les images PNG")
    parser.add_argument("--key", help="Clé spécifique à utiliser dans le NPZ")
    parser.add_argument("--prefix", default="slice", help="Préfixe pour les noms de fichiers (défaut: slice)")
    parser.add_argument("--image-type", choices=["auto", "imagesTr", "labelsTr"], default="auto",
                       help="Type d'images (défaut: auto)")
    parser.add_argument("--uint8", action="store_true", help="Export en uint8 (sinon RGB/couleurs)")
    parser.add_argument("--dpi", type=int, help="Résolution des images (défaut: résolution du volume)")
    parser.add_argument("--rotation", type=int, choices=[0, 90, 180, 270], default=0,
                       help="Rotation à appliquer en degrés (défaut: 0)")
    parser.add_argument("--start", type=int, help="Index de début pour export partiel")
    parser.add_argument("--end", type=int, help="Index de fin pour export partiel")
    parser.add_argument("--list-keys", action="store_true", help="Lister les clés disponibles et quitter")
    
    args = parser.parse_args()
    
    # Vérifier que le fichier d'entrée existe
    if not os.path.exists(args.input_file):
        print(f"❌ Erreur : Le fichier {args.input_file} n'existe pas")
        return
    
    try:
        # Si on veut juste lister les clés
        if args.list_keys:
            npz_data = np.load(args.input_file)
            print(f"\n=== CLÉS DISPONIBLES DANS {args.input_file} ===")
            for key in npz_data.keys():
                data = npz_data[key]
                print(f"Clé: '{key}' - Shape: {data.shape}, Type: {data.dtype}")
            return
        
        # Créer le dossier de sortie basé sur le nom du fichier d'entrée
        input_path = Path(args.input_file)
        volume_name = input_path.stem  # Nom sans extension
        output_dir = Path(args.output_dir) / volume_name

        # Déterminer la résolution par défaut
        if args.dpi is None:
            # Utiliser la résolution du volume (approximation basée sur les dimensions)
            temp_npz = np.load(args.input_file)
            # Prendre la première clé pour estimer les dimensions
            first_key = list(temp_npz.keys())[0]
            temp_volume = temp_npz[first_key]
            if temp_volume.ndim >= 2:
                avg_dim = (temp_volume.shape[-2] + temp_volume.shape[-1]) / 2
                if avg_dim > 512:
                    default_dpi = 300
                elif avg_dim > 256:
                    default_dpi = 200
                else:
                    default_dpi = 150
            else:
                default_dpi = 150
        else:
            default_dpi = args.dpi

        # Créer l'exporteur
        exporter = NPZToPNGExporter(
            npz_path=args.input_file,
            output_dir=str(output_dir),
            data_key=args.key,
            prefix=args.prefix,
            image_type=args.image_type,
            export_uint8=args.uint8,
            rotation=args.rotation
        )
        
        # Exporter selon les paramètres
        if args.start is not None and args.end is not None:
            exporter.export_slice_range(args.start, args.end, dpi=default_dpi)
        else:
            exporter.export_all_slices(dpi=default_dpi)
            
    except Exception as e:
        print(f"❌ Erreur lors de l'export : {str(e)}")


if __name__ == "__main__":
    # Si exécuté directement, utiliser les paramètres par défaut ou l'interface CLI
    import sys
    
    if len(sys.argv) == 1:
        # Mode interactif simple
        print("=== Exporteur NPZ vers PNG ===")
        npz_file = input("Chemin vers le fichier NPZ : ").strip()
        output_dir = input("Dossier de sortie : ").strip()
        
        if npz_file and output_dir:
            exporter = NPZToPNGExporter(npz_file, output_dir)
            exporter.export_all_slices()
        else:
            print("❌ Paramètres manquants")
    else:
        # Mode ligne de commande
        main()
