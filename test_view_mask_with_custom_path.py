#!/usr/bin/env python3
"""
Test de view_mask.py avec un chemin personnalisé
Simule ce que fait l'interface graphique
"""

import os
import sys
import tempfile
import re

def create_temp_visualize_mask_script(file_path):
    """Version exacte de la fonction corrigée de l'interface graphique"""
    temp_script = "temp_visualize_mask_test.py"
    with open("view_mask.py", 'r', encoding='utf-8') as f:
        content = f.read()

    # Remplacer les emojis par du texte simple
    content = content.replace('❌', '[ERREUR]')
    content = content.replace('→', '->')

    # Méthode plus robuste : remplacer toute ligne qui définit image_path
    # Échapper les backslashes dans le chemin pour éviter les erreurs regex
    escaped_file_path = file_path.replace('\\', '\\\\')
    
    # Pattern pour capturer n'importe quelle définition d'image_path
    pattern = r'image_path\s*=\s*r?"[^"]*"'
    replacement = f'image_path = r"{file_path}"'
    content = re.sub(pattern, replacement, content)
    
    # Si aucun remplacement n'a été fait, chercher d'autres patterns
    if file_path not in content:
        # Pattern alternatif pour les chemins avec des apostrophes
        pattern2 = r"image_path\s*=\s*r?'[^']*'"
        content = re.sub(pattern2, replacement, content)
        
        # Si toujours pas trouvé, chercher et remplacer l'appel à visualize_image
        if file_path not in content:
            # Chercher l'appel à visualize_image avec un paramètre
            pattern3 = r'(visualize_image\()[^)]*(\))'
            replacement3 = f'\\1r"{file_path}"\\2'
            content = re.sub(pattern3, replacement3, content)

    with open(temp_script, 'w', encoding='utf-8') as f:
        f.write(content)

    return temp_script

def test_with_custom_path():
    """Test avec un chemin personnalisé"""
    # Utiliser une image de test qui existe
    test_image = "test_labels/test_label_000000.png"
    
    if not os.path.exists(test_image):
        print(f"❌ Image de test non trouvée: {test_image}")
        return False
    
    print(f"🧪 Test avec l'image: {test_image}")
    
    try:
        # Créer le script temporaire
        temp_script = create_temp_visualize_mask_script(test_image)
        
        print(f"✅ Script temporaire créé: {temp_script}")
        
        # Exécuter le script temporaire
        print("🚀 Exécution du script temporaire...")
        import subprocess
        result = subprocess.run([sys.executable, temp_script], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Script exécuté avec succès!")
            print("Sortie:")
            print(result.stdout)
        else:
            print("❌ Erreur lors de l'exécution:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
        
        # Nettoyer
        if os.path.exists(temp_script):
            os.remove(temp_script)
            print("🧹 Fichier temporaire nettoyé")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Test de view_mask.py avec chemin personnalisé")
    print("=" * 60)
    
    success = test_with_custom_path()
    
    print("=" * 60)
    if success:
        print("✅ Test réussi! L'interface graphique devrait maintenant fonctionner correctement.")
    else:
        print("❌ Test échoué. Il y a encore des problèmes à résoudre.")
