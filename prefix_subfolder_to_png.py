#!/usr/bin/env python3
"""
Script pour préfixer les noms de fichiers PNG par le nom de leur sous-dossier parent.
Exemple : dossierA/sous1/1.png -> dossierA/sous1/sous1_1.png
"""
import os
from pathlib import Path
import argparse
import sys

# === Chemin du dossier racine à modifier ici ===
ROOT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\inference-Dataset028_TPI_calibprod_light"  # <-- À modifier


def prefix_pngs_in_subfolders(root_folder):
    root = Path(root_folder)
    if not root.exists() or not root.is_dir():
        raise FileNotFoundError(f"Le dossier {root_folder} n'existe pas ou n'est pas un dossier.")

    subfolders = [f for f in root.iterdir() if f.is_dir()]
    if not subfolders:
        print(f"Aucun sous-dossier trouvé dans {root_folder}")
        return

    for sub in subfolders:
        pngs = list(sub.glob('*.png'))
        if not pngs:
            continue
        for png_file in pngs:
            new_name = f"{sub.name}_{png_file.name}"
            new_path = png_file.parent / new_name
            if new_path.exists():
                print(f"⚠️ Le fichier {new_path} existe déjà, saut...")
                continue
            png_file.rename(new_path)
            print(f"✅ {png_file} -> {new_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Préfixe les PNGs par le nom du sous-dossier.")
    parser.add_argument("root_folder", nargs='?', default=ROOT_FOLDER,
                        help="Dossier racine contenant les sous-dossiers de PNGs (défaut: valeur hardcodée)")
    args = parser.parse_args()
    try:
        prefix_pngs_in_subfolders(args.root_folder)
    except Exception as e:
        print(f"❌ Erreur : {e}")
        sys.exit(1) 