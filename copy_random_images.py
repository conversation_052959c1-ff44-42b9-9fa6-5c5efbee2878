import os
import random
import shutil
import json
from pathlib import Path
from tkinter import Tk, filedialog, messagebox

# === Constantes ===
CONFIG_FILE = "last_paths.json"
nombre_images = 20
copie = 1  # 1 = copier, 0 = déplacer
ajout_suffixe = 0  # 1 = suffixe, 0 = préfixe
copier_tout = 0  # 1 = copier toutes les images, 0 = copier nombre_images images

# === Charger les derniers chemins depuis le fichier JSON ===
def charger_chemins():
    if Path(CONFIG_FILE).exists():
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)
    return {"source": str(Path.home()), "destination": str(Path.home())}

# === Sauvegarder les chemins sélectionnés ===
def sauvegarder_chemins(source, destination):
    with open(CONFIG_FILE, "w") as f:
        json.dump({"source": str(source), "destination": str(destination)}, f)

# === Fonction principale ===
def executer_operation():
    # === Charger les derniers chemins depuis le fichier JSON ===
    chemins = charger_chemins()

    # === Sélection des dossiers via GUI ===
    source_dir = Path(filedialog.askdirectory(title="Choisir le dossier source", initialdir=chemins["source"]))
    if not source_dir:  # Si l'utilisateur annule la sélection
        return False
    destination_dir = Path(filedialog.askdirectory(title="Choisir le dossier destination", initialdir=chemins["destination"]))
    if not destination_dir:  # Si l'utilisateur annule la sélection
        return False
    
    sauvegarder_chemins(source_dir, destination_dir)

    # === Préparer le nom à ajouter basé sur le nom du dossier source ===
    ajout_nom = source_dir.name
    destination_dir.mkdir(parents=True, exist_ok=True)

    # === Lister les fichiers images ===
    extensions_images = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"}
    toutes_les_images = [f for f in source_dir.iterdir() if f.suffix.lower() in extensions_images]

    if not toutes_les_images:
        messagebox.showerror("Erreur", "Aucune image trouvée dans le dossier source.")
        return True

    if not copier_tout and len(toutes_les_images) < nombre_images:
        messagebox.showerror("Erreur", f"Seulement {len(toutes_les_images)} images trouvées, impossible d'en extraire {nombre_images}.")
        return True

    # === Sélection aléatoire ou toutes les images ===
    images_selectionnees = toutes_les_images if copier_tout else random.sample(toutes_les_images, nombre_images)

    # === Copie ou déplacement ===
    for image_path in images_selectionnees:
        if ajout_suffixe:
            nouveau_nom = image_path.stem + f"_{ajout_nom}" + image_path.suffix
        else:
            nouveau_nom = f"{ajout_nom}_" + image_path.name
        destination_path = destination_dir / nouveau_nom

        if copie:
            shutil.copy(image_path, destination_path)
        else:
            shutil.move(str(image_path), destination_path)

    operation = "copiées" if copie else "déplacées"
    return messagebox.askyesno("Succès", f"{len(images_selectionnees)} images {operation} avec succès dans {destination_dir}\n\nVoulez-vous effectuer une autre opération ?")

# === Boucle principale ===
Tk().withdraw()  # Cacher la fenêtre principale
while True:
    if not executer_operation():
        break
