import os

# === 🔧 À MODIFIER : Chemin du dossier à explorer ===
dossier_racine = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\NDEtoImagesUniversel\images"

def afficher_arborescence(dossier, prefixe='', niveau_max=5, niveau_actuel=0, afficher_fichiers=True):
    if niveau_actuel > niveau_max:
        return ""
    
    contenu = []
    try:
        elements = sorted(os.listdir(dossier))
    except PermissionError:
        return f"{prefixe}[Permission Denied]\n"

    for i, nom in enumerate(elements):
        chemin = os.path.join(dossier, nom)
        if not os.path.isdir(chemin) and not afficher_fichiers:
            continue

        dernier = (i == len(elements) - 1)
        branche = '└── ' if dernier else '├── '
        sous_prefixe = '    ' if dernier else '│   '
        ligne = f"{prefixe}{branche}{nom}\n"
        contenu.append(ligne)

        if os.path.isdir(chemin):
            contenu.append(afficher_arborescence(chemin, prefixe + sous_prefixe, niveau_max, niveau_actuel + 1, afficher_fichiers))
    
    return ''.join(contenu)

def main():
    if not os.path.exists(dossier_racine):
        print(f"❌ Dossier introuvable : {dossier_racine}")
        return

    choix = input("Afficher aussi les fichiers ? (o/n) : ").strip().lower()
    afficher_fichiers = choix == 'o'

    print(f"📂 Structure de : {dossier_racine}\n")
    arbo = f"{os.path.basename(dossier_racine)}\n"
    arbo += afficher_arborescence(dossier_racine, afficher_fichiers=afficher_fichiers)

    print(arbo)

    with open("structure.txt", "w", encoding="utf-8") as f:
        f.write(arbo)
    print("\n✅ Arborescence sauvegardée dans 'structure.txt'.")

if __name__ == "__main__":
    main()
