import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import os
from pathlib import Path
import argparse
from PIL import Image

# Import des utilitaires pour les couleurs NDE
try:
    from utils.omniscan_div_cmap import get_omniscan_diverging_colormap
    from utils.utils_linear_scan_alongv import safe_division
except ImportError:
    print("⚠️ Modules utils non trouvés, utilisation des colormaps par défaut")
    def get_omniscan_diverging_colormap(path):
        return plt.cm.viridis
    def safe_division(num, den):
        return num / den if den != 0 else 0.0

class H5ToPNGExporter:
    def __init__(self, h5_path, output_dir, dataset_path=None, prefix="slice", image_type="auto",
                 export_uint8=False, rotation=0, omniscan_colormap_path='./OmniScanColorMap.npy'):
        """
        Exporte toutes les slices d'un volume H5 en images PNG

        Args:
            h5_path: Chemin vers le fichier H5
            output_dir: Dossier de sortie pour les images PNG
            dataset_path: Chemin spécifique du dataset à utiliser dans le H5 (None pour sélection automatique)
            prefix: Préfixe pour les noms de fichiers (défaut: "slice")
            image_type: Type d'images ("auto", "imagesTr", "labelsTr")
            export_uint8: Si True, export en uint8, sinon RGB pour imagesTr ou couleurs distinctes pour labelsTr
            rotation: Rotation à appliquer en degrés (0, 90, 180, 270)
            omniscan_colormap_path: Chemin vers le fichier OmniScanColorMap.npy
        """
        self.h5_path = h5_path
        self.output_dir = Path(output_dir)
        self.prefix = prefix
        self.image_type = image_type
        self.export_uint8 = export_uint8
        self.rotation = rotation
        self.omniscan_colormap_path = omniscan_colormap_path
        
        # Créer le dossier de sortie s'il n'existe pas
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Charger le fichier H5
        print(f"📂 Chargement du fichier H5 : {h5_path}")
        self.h5_file = h5py.File(h5_path, 'r')
        
        # Afficher la structure
        print("📊 Structure du fichier H5 :")
        self._print_h5_structure(self.h5_file, max_depth=2)
        
        # Sélectionner le dataset
        self.dataset_path = self._select_dataset(dataset_path)
        print(f"🔑 Utilisation du dataset : '{self.dataset_path}'")
        
        # Charger les données du volume
        dataset = self.h5_file[self.dataset_path]
        self.volume_raw = np.array(dataset)  # Garder les données originales

        # Analyser les données pour déterminer le type
        self.min_value = float(np.min(self.volume_raw))
        self.max_value = float(np.max(self.volume_raw))
        self.is_nde_data = self._detect_nde_data()

        print(f"📊 Données brutes : min={self.min_value:.2f}, max={self.max_value:.2f}")
        print(f"📊 Type NDE détecté : {'Oui' if self.is_nde_data else 'Non'}")

        # Détection automatique du type d'images (en plus de la détection NDE)
        unique_values = np.unique(self.volume_raw)
        self.detected_type = self._detect_image_type(unique_values)

        # Utiliser le type spécifié ou celui détecté
        if self.image_type == "auto":
            self.final_image_type = self.detected_type
        else:
            self.final_image_type = self.image_type

        print(f"🏷️ Type d'images : {self.final_image_type}")
        print(f"🎨 Mode export : {'uint8' if self.export_uint8 else 'RGB/Couleurs'}")

        # Préparer les données selon le type et le mode d'export
        self._prepare_data()
        
        # Assurer que le volume a au moins 3 dimensions
        if self.volume.ndim == 2:
            self.volume = self.volume[np.newaxis, :, :]
        elif self.volume.ndim > 3:
            print(f"⚠️ Volume avec {self.volume.ndim} dimensions détecté. Utilisation des 3 premières dimensions.")
            self.volume = self.volume[:, :, :] if self.volume.ndim == 4 else self.volume
        
        print(f"📊 Volume chargé : {self.volume.shape} (Z, Y, X)")
        print(f"📈 Valeurs uniques : {np.unique(self.volume)}")

        # Configuration du colormap
        self._setup_colormap()

    def _print_h5_structure(self, group, prefix="", max_depth=3, current_depth=0):
        """Affiche la structure du fichier H5"""
        if current_depth >= max_depth:
            return
            
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Group):
                print(f"{prefix}📁 Groupe: {key}/")
                self._print_h5_structure(item, prefix + "  ", max_depth, current_depth + 1)
            elif isinstance(item, h5py.Dataset):
                print(f"{prefix}📄 Dataset: {key} - Shape: {item.shape}, Type: {item.dtype}")

    def _find_datasets(self, group, path=""):
        """Trouve tous les datasets dans le fichier H5"""
        datasets = []
        for key in group.keys():
            item = group[key]
            current_path = f"{path}/{key}" if path else key
            if isinstance(item, h5py.Group):
                datasets.extend(self._find_datasets(item, current_path))
            elif isinstance(item, h5py.Dataset):
                # Filtrer les datasets qui ressemblent à des volumes (au moins 2D)
                if len(item.shape) >= 2:
                    datasets.append(current_path)
        return datasets
    
    def _select_dataset(self, dataset_path):
        """Sélectionne le dataset à utiliser"""
        available_datasets = self._find_datasets(self.h5_file)
        
        if dataset_path is not None:
            if dataset_path in available_datasets:
                return dataset_path
            else:
                print(f"⚠️ Dataset '{dataset_path}' non trouvé. Datasets disponibles : {available_datasets}")
        
        # Sélection automatique
        if len(available_datasets) == 0:
            raise ValueError("Aucun dataset trouvé dans le fichier H5")
        elif len(available_datasets) == 1:
            return available_datasets[0]
        
        # Afficher les options et demander à l'utilisateur
        print("Plusieurs datasets disponibles. Sélectionnez un dataset :")
        for i, dataset_path in enumerate(available_datasets):
            dataset = self.h5_file[dataset_path]
            shape = dataset.shape
            dtype = dataset.dtype
            print(f"  {i}: '{dataset_path}' - Shape: {shape}, Type: {dtype}")
        
        while True:
            try:
                choice = input("Entrez le numéro du dataset à utiliser (ou le chemin complet) : ").strip()
                if choice.isdigit():
                    idx = int(choice)
                    if 0 <= idx < len(available_datasets):
                        return available_datasets[idx]
                elif choice in available_datasets:
                    return choice
                else:
                    print("Choix invalide. Veuillez réessayer.")
            except (ValueError, IndexError):
                print("Choix invalide. Veuillez réessayer.")

    def _detect_nde_data(self):
        """Détecte si les données sont des données NDE (amplitudes)"""
        dtype = self.volume_raw.dtype
        dataset_name = self.dataset_path.lower()

        # Vérifier le type de données
        is_amplitude_type = dtype in [np.int16, np.uint16, np.int32, np.uint32]

        # Vérifier les mots-clés dans le nom du dataset
        nde_keywords = ['amplitude', 'endview', 'dscan', 'cscan', 'ascan', 'ultrasonic', 'nde']
        has_nde_keywords = any(keyword in dataset_name for keyword in nde_keywords)

        # Vérifier la plage de valeurs (typique des amplitudes NDE)
        value_range = self.max_value - self.min_value
        has_nde_range = value_range > 100 and (self.max_value > 1000 or self.min_value < -100)

        return is_amplitude_type and (has_nde_keywords or has_nde_range)

    def _normalize_nde_data(self):
        """Normalise les données NDE selon la méthode standard"""
        # Normalisation comme dans gen_endview
        normalized = safe_division(self.volume_raw - self.min_value, self.max_value - self.min_value)

        # Convertir en uint8 pour l'affichage
        return (normalized * 255).astype(np.uint8)

    def _detect_image_type(self, unique_values):
        """Détecte si le volume contient des images (imagesTr) ou des labels (labelsTr)"""
        # Si c'est des données NDE, c'est forcément des images
        if self.is_nde_data:
            return "imagesTr"

        # Critères pour détecter des labels :
        # 1. Valeurs entières uniquement
        # 2. Valeurs dans une plage limitée (typiquement 0-4 pour les classes)
        # 3. Nombre limité de valeurs uniques

        # Vérifier si toutes les valeurs sont des entiers
        if not np.allclose(unique_values, unique_values.astype(int)):
            return "imagesTr"  # Valeurs continues -> images

        # Vérifier si les valeurs correspondent à des classes de segmentation
        if unique_values.max() <= 10 and unique_values.min() >= 0:
            if len(unique_values) <= 15:  # Nombre raisonnable de classes
                return "labelsTr"

        return "imagesTr"  # Par défaut, considérer comme des images

    def _prepare_data(self):
        """Prépare les données selon le type d'images et le mode d'export"""
        if self.is_nde_data:
            # Pour les données NDE, utiliser la normalisation spéciale
            self.volume = self._normalize_nde_data()
        elif self.final_image_type == "labelsTr":
            # Pour les labels, garder les valeurs exactes
            self.volume = self.volume_raw.astype(np.uint8)
        else:
            # Pour les images, normaliser vers 0-255
            if self.volume_raw.dtype in [np.float32, np.float64]:
                # Normaliser les données float
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > vmin:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = np.zeros_like(self.volume_raw, dtype=np.uint8)
            else:
                # Données entières, normaliser selon la plage
                vmin, vmax = self.volume_raw.min(), self.volume_raw.max()
                if vmax > 255:
                    normalized = (self.volume_raw - vmin) / (vmax - vmin)
                    self.volume = (normalized * 255).astype(np.uint8)
                else:
                    self.volume = self.volume_raw.astype(np.uint8)


    def _setup_colormap(self):
        """Configure le colormap selon le type d'images et le mode d'export"""
        if self.is_nde_data:
            # Pour les données NDE, utiliser les colormaps OmniScan
            if self.export_uint8:
                print("🎨 Données NDE : Export en niveaux de gris (uint8)")
                self.cmap = 'gray'
            else:
                print("🎨 Données NDE : Export RGB avec colormap OmniScan")
                try:
                    if os.path.exists(self.omniscan_colormap_path):
                        if self.min_value < 0:
                            # Données avec valeurs négatives -> colormap divergent
                            print("🎨 Utilisation du colormap OmniScan divergent (valeurs négatives)")
                            self.cmap = get_omniscan_diverging_colormap(self.omniscan_colormap_path)
                        else:
                            # Données positives -> colormap standard
                            print("🎨 Utilisation du colormap OmniScan standard")
                            omniscan_array = np.load(self.omniscan_colormap_path)
                            self.cmap = ListedColormap(omniscan_array)
                    else:
                        print("⚠️ Fichier OmniScanColorMap.npy non trouvé, utilisation de 'viridis'")
                        self.cmap = 'viridis'
                except Exception as e:
                    print(f"⚠️ Erreur lors du chargement du colormap OmniScan: {e}")
                    self.cmap = 'viridis'
        elif self.final_image_type == "labelsTr":
            if self.export_uint8:
                print("🎨 Labels : Export direct des classes (uint8)")
                self.cmap = None  # Pas de colormap, export direct
            else:
                print("🎨 Labels : Export avec couleurs distinctes pour visualisation")
                # Colormap avec couleurs distinctes pour les classes
                self.cmap = ListedColormap([
                    (0, 0, 0),        # 0: background - noir
                    (0, 0, 1),        # 1: classe 1 - bleu
                    (0, 1, 0),        # 2: classe 2 - vert
                    (1, 0, 0),        # 3: classe 3 - rouge
                    (1, 1, 0),        # 4: classe 4 - jaune
                    (1, 0, 1),        # 5: classe 5 - magenta
                    (0, 1, 1),        # 6: classe 6 - cyan
                    (0.5, 0.5, 0.5),  # 7: classe 7 - gris
                    (1, 0.5, 0),      # 8: classe 8 - orange
                    (0.5, 0, 0.5),    # 9: classe 9 - violet
                ])
        else:  # imagesTr
            if self.export_uint8:
                print("🎨 Images : Export en niveaux de gris (uint8)")
                self.cmap = 'gray'
            else:
                print("🎨 Images : Export en RGB avec colormap")
                self.cmap = 'viridis'  # Colormap par défaut pour les images

        # Définir les limites de valeurs
        self.vmin, self.vmax = 0, 255
    
    def export_all_slices(self, dpi=150, figsize=(8, 6)):
        """
        Exporte toutes les slices en images PNG
        
        Args:
            dpi: Résolution des images (défaut: 150)
            figsize: Taille de la figure (défaut: (8, 6))
        """
        num_slices = self.volume.shape[0]
        print(f"🚀 Début de l'export de {num_slices} slices...")
        
        for slice_idx in range(num_slices):
            self._export_slice(slice_idx, dpi, figsize)
            
            # Afficher le progrès
            if (slice_idx + 1) % 10 == 0 or slice_idx == num_slices - 1:
                progress = ((slice_idx + 1) / num_slices) * 100
                print(f"📈 Progrès : {slice_idx + 1}/{num_slices} ({progress:.1f}%)")
        
        print(f"✅ Export terminé ! {num_slices} images sauvegardées dans : {self.output_dir}")
        
    def _apply_rotation(self, data):
        """Applique la rotation spécifiée aux données (sens horaire)"""
        if self.rotation == 0:
            return data
        elif self.rotation == 90:
            return np.rot90(data, k=-1)  # Rotation horaire de 90°
        elif self.rotation == 180:
            return np.rot90(data, k=2)   # Rotation de 180°
        elif self.rotation == 270:
            return np.rot90(data, k=1)   # Rotation horaire de 270° = anti-horaire de 90°
        else:
            return data

    def _export_slice(self, slice_idx, dpi, figsize):
        """Exporte une slice spécifique selon le mode choisi"""
        current_data = self.volume[slice_idx, :, :]

        # Appliquer la rotation si nécessaire
        current_data = self._apply_rotation(current_data)

        # Créer le nom de fichier avec sérialisation simple à 4 chiffres
        filename = f"{slice_idx + 1:04d}.png"
        filepath = self.output_dir / filename

        if self.final_image_type == "labelsTr" and self.export_uint8:
            # Export direct des classes sans colormap
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)
        elif not self.export_uint8 and self.cmap is not None:
            # Export RGB avec colormap
            if hasattr(self.cmap, '__call__'):
                # Colormap matplotlib
                if self.final_image_type == "labelsTr":
                    # Pour les labels, normaliser selon le nombre de classes
                    max_class = current_data.max()
                    if max_class > 0:
                        normalized_data = current_data / max_class
                    else:
                        normalized_data = current_data
                else:
                    # Pour les images, normaliser vers 0-1
                    normalized_data = current_data / 255.0

                colored_data = self.cmap(normalized_data)
                rgb_data = (colored_data[:, :, :3] * 255).astype(np.uint8)
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
            else:
                # Fallback : convertir en RGB grayscale
                rgb_data = np.stack([current_data] * 3, axis=-1)
                img = Image.fromarray(rgb_data, mode='RGB')
                img.save(filepath)
        else:
            # Export uint8 en grayscale
            img = Image.fromarray(current_data, mode='L')
            img.save(filepath)

    def _export_with_matplotlib(self, data, filepath, dpi, figsize):
        """Export avec matplotlib (fallback)"""
        # Calculer figsize basé sur les dimensions réelles des données
        height, width = data.shape
        # Utiliser les dimensions réelles en pouces pour préserver les proportions
        fig_width = width / dpi
        fig_height = height / dpi

        fig, ax = plt.subplots(figsize=(fig_width, fig_height))
        ax.imshow(data, cmap=self.cmap, vmin=self.vmin, vmax=self.vmax)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.axis('off')
        plt.savefig(filepath, dpi=dpi, bbox_inches='tight', pad_inches=0, facecolor='white')
        plt.close(fig)
    
    def export_slice_range(self, start_slice, end_slice, dpi=150, figsize=(8, 6)):
        """
        Exporte une plage de slices
        
        Args:
            start_slice: Index de début (inclus)
            end_slice: Index de fin (inclus)
            dpi: Résolution des images
            figsize: Taille de la figure
        """
        num_slices = self.volume.shape[0]
        start_slice = max(0, start_slice)
        end_slice = min(num_slices - 1, end_slice)
        
        print(f"🚀 Export des slices {start_slice} à {end_slice}...")
        
        for slice_idx in range(start_slice, end_slice + 1):
            self._export_slice(slice_idx, dpi, figsize)
        
        exported_count = end_slice - start_slice + 1
        print(f"✅ Export terminé ! {exported_count} images sauvegardées dans : {self.output_dir}")
    

    def get_slice_info(self, slice_idx):
        """Retourne des informations sur une slice spécifique"""
        if slice_idx < 0 or slice_idx >= self.volume.shape[0]:
            return None
        
        current_data = self.volume[slice_idx, :, :]
        unique_values, counts = np.unique(current_data, return_counts=True)
        
        info = {
            'slice_index': slice_idx,
            'shape': current_data.shape,
            'min_value': current_data.min(),
            'max_value': current_data.max(),
            'unique_values': unique_values,
            'value_counts': counts,
            'is_mask': self.is_mask,
            'dataset_path': self.dataset_path
        }
        
        return info
    
    def list_available_datasets(self):
        """Liste tous les datasets disponibles avec leurs informations"""
        print(f"\n=== DATASETS DISPONIBLES DANS {self.h5_path} ===")
        datasets = self._find_datasets(self.h5_file)
        for dataset_path in datasets:
            dataset = self.h5_file[dataset_path]
            print(f"Dataset: '{dataset_path}'")
            print(f"  Shape: {dataset.shape}")
            print(f"  Type: {dataset.dtype}")
            print(f"  Min/Max: {dataset[:].min()}/{dataset[:].max()}")
            print(f"  Valeurs uniques: {len(np.unique(dataset[:]))}")
            print()

    def __del__(self):
        """Ferme le fichier H5 lors de la destruction de l'objet"""
        if hasattr(self, 'h5_file'):
            self.h5_file.close()


def main():
    """Fonction principale avec interface en ligne de commande"""
    parser = argparse.ArgumentParser(description="Exporte les slices d'un volume H5 en images PNG")
    parser.add_argument("input_file", help="Chemin vers le fichier H5")
    parser.add_argument("output_dir", help="Dossier de sortie pour les images PNG")
    parser.add_argument("--dataset", help="Chemin spécifique du dataset à utiliser dans le H5")
    parser.add_argument("--prefix", default="slice", help="Préfixe pour les noms de fichiers (défaut: slice)")
    parser.add_argument("--colormap", choices=["auto", "gray", "mask"], default="auto", 
                       help="Type de colormap (défaut: auto)")
    parser.add_argument("--dpi", type=int, default=150, help="Résolution des images (défaut: 150)")
    parser.add_argument("--rotation", type=int, choices=[0, 90, 180, 270], default=0,
                       help="Rotation à appliquer en degrés (défaut: 0)")
    parser.add_argument("--start", type=int, help="Index de début pour export partiel")
    parser.add_argument("--end", type=int, help="Index de fin pour export partiel")
    parser.add_argument("--list-datasets", action="store_true", help="Lister les datasets disponibles et quitter")
    
    args = parser.parse_args()
    
    # Vérifier que le fichier d'entrée existe
    if not os.path.exists(args.input_file):
        print(f"❌ Erreur : Le fichier {args.input_file} n'existe pas")
        return
    
    try:
        # Si on veut juste lister les datasets
        if args.list_datasets:
            print(f"\n=== DATASETS DISPONIBLES DANS {args.input_file} ===")
            # Créer un exporteur temporaire juste pour lister les datasets
            temp_exporter = H5ToPNGExporter(args.input_file, "temp", image_type="auto")
            temp_exporter.list_available_datasets()
            return
        
        # Créer le dossier de sortie basé sur le nom du fichier d'entrée
        input_path = Path(args.input_file)
        volume_name = input_path.stem  # Nom sans extension
        output_dir = Path(args.output_dir) / volume_name

        # Créer l'exporteur
        exporter = H5ToPNGExporter(
            h5_path=args.input_file,
            output_dir=str(output_dir),
            dataset_path=args.dataset,
            prefix=args.prefix,
            image_type=args.colormap,
            rotation=args.rotation
        )
        
        # Exporter selon les paramètres
        if args.start is not None and args.end is not None:
            exporter.export_slice_range(args.start, args.end, dpi=args.dpi)
        else:
            exporter.export_all_slices(dpi=args.dpi)
            
    except Exception as e:
        print(f"❌ Erreur lors de l'export : {str(e)}")


if __name__ == "__main__":
    # Si exécuté directement, utiliser les paramètres par défaut ou l'interface CLI
    import sys
    
    if len(sys.argv) == 1:
        # Mode interactif simple
        print("=== Exporteur H5 vers PNG ===")
        h5_file = input("Chemin vers le fichier H5 : ").strip()
        output_dir = input("Dossier de sortie : ").strip()
        
        if h5_file and output_dir:
            exporter = H5ToPNGExporter(h5_file, output_dir)
            exporter.export_all_slices()
        else:
            print("❌ Paramètres manquants")
    else:
        # Mode ligne de commande
        main()
