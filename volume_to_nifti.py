import numpy as np
import h5py
from pathlib import Path

# === CONFIGURATION HARDCODÉE ===
# Modifiez ces chemins selon vos besoins

# MODE FICHIER UNIQUE
INPUT_FILE = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\PS-00400_MX2-757-SC-PS-00400-Z-46000_raw_data.h5"  # ou .h5
OUTPUT_FILE = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\PS-00400_MX2-757-SC-PS-00400-Z-46000_raw_data.nii.gz"

# MODE DOSSIER (traitement en lot)
# Laissez INPUT_FOLDER vide pour utiliser le mode fichier unique
INPUT_FOLDER = r""  # Dossier contenant les fichiers .h5 ou .npz
OUTPUT_FOLDER = r""  # Dossier de sortie pour les fichiers .nii.gz

# Pour les fichiers .npz, spécifiez la clé si nécessaire (None pour sélection automatique)
NPZ_DATA_KEY = None

# Pour les fichiers .h5, spécifiez le dataset si nécessaire (None pour sélection automatique)
H5_DATASET_PATH = None


class VolumeToNIfTIConverter:
    def __init__(self, input_path, output_path, npz_key=None, h5_dataset=None):
        """
        Convertit un volume .npz ou .h5 en format NIfTI
        
        Args:
            input_path: Chemin vers le fichier d'entrée (.npz ou .h5)
            output_path: Chemin de sortie (.nii.gz)
            npz_key: Clé spécifique pour fichier NPZ (None pour sélection automatique)
            h5_dataset: Dataset spécifique pour fichier H5 (None pour sélection automatique)
        """
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.npz_key = npz_key
        self.h5_dataset = h5_dataset
        
        # Vérifications
        if not self.input_path.exists():
            raise FileNotFoundError(f"Le fichier d'entrée n'existe pas : {input_path}")
        
        # Déterminer le type de fichier
        self.file_type = self._detect_file_type()
        
        # Créer le dossier de sortie si nécessaire
        self.output_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _detect_file_type(self):
        """Détecte le type de fichier d'entrée"""
        suffix = self.input_path.suffix.lower()
        if suffix == '.npz':
            return 'npz'
        elif suffix in ['.h5', '.hdf5']:
            return 'h5'
        else:
            raise ValueError(f"Type de fichier non supporté : {suffix}. Utilisez .npz ou .h5/.hdf5")
    
    def convert(self):
        """Effectue la conversion complète"""
        print(f"🚀 Conversion de volume vers NIfTI")
        print(f"📂 Fichier source : {self.input_path}")
        print(f"📁 Fichier sortie : {self.output_path}")
        print(f"🎨 Type : {self.file_type.upper()}")
        
        # Charger le volume selon le type
        if self.file_type == 'npz':
            volume = self._load_npz_volume()
        elif self.file_type == 'h5':
            volume = self._load_h5_volume()
        
        # Sauvegarder en NIfTI
        self._save_nifti(volume)
        
        print(f"✅ Conversion terminée avec succès !")
        return True
    
    def _load_npz_volume(self):
        """Charge un volume depuis un fichier NPZ"""
        print(f"📂 Chargement du fichier NPZ...")
        npz_data = np.load(self.input_path)
        
        print(f"📊 Clés disponibles : {list(npz_data.keys())}")
        
        # Sélectionner la clé de données
        if self.npz_key is not None:
            if self.npz_key not in npz_data.keys():
                raise KeyError(f"Clé '{self.npz_key}' non trouvée dans le fichier NPZ")
            data_key = self.npz_key
            print(f"🔑 Utilisation de la clé spécifiée : '{data_key}'")
        else:
            data_key = self._select_best_npz_key(npz_data)
            print(f"🔑 Sélection automatique de la clé : '{data_key}'")
        
        # Charger les données
        volume = npz_data[data_key]
        print(f"📊 Volume chargé : {volume.shape}, type: {volume.dtype}")
        
        return volume
    
    def _select_best_npz_key(self, npz_data):
        """Sélectionne automatiquement la meilleure clé dans un fichier NPZ"""
        candidates = []
        
        for key in npz_data.keys():
            data = npz_data[key]
            shape = data.shape
            dtype = data.dtype
            
            # Calculer un score pour cette clé
            score = 0
            
            # Préférer les types numériques
            if np.issubdtype(dtype, np.integer) or np.issubdtype(dtype, np.floating):
                score += 100
            elif dtype == bool:
                score += 50
            
            # Préférer les données 3D
            if len(shape) == 3:
                score += 200
                # Bonus pour les dimensions raisonnables
                if all(dim > 1 for dim in shape):
                    score += 100
                # Préférer uint8 pour les masques/labels
                if dtype == np.uint8:
                    score += 50
            elif len(shape) == 2:
                score += 50
            elif len(shape) == 1:
                score += 1
            
            candidates.append((key, score, shape, dtype))
        
        # Trier par score décroissant
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        if candidates and candidates[0][1] > 0:
            selected_key = candidates[0][0]
            print(f"[AUTO] Sélection automatique : '{selected_key}' (score: {candidates[0][1]})")
            print(f"   Shape: {candidates[0][2]}, Type: {candidates[0][3]}")
            return selected_key
        else:
            raise ValueError("Aucune clé appropriée trouvée dans le fichier NPZ")
    
    def _load_h5_volume(self):
        """Charge un volume depuis un fichier H5"""
        print(f"📂 Chargement du fichier H5...")
        
        with h5py.File(self.input_path, 'r') as h5_file:
            # Afficher la structure
            print("📊 Structure du fichier H5 :")
            self._print_h5_structure(h5_file, max_depth=2)
            
            # Sélectionner le dataset
            if self.h5_dataset is not None:
                if self.h5_dataset not in h5_file:
                    raise KeyError(f"Dataset '{self.h5_dataset}' non trouvé dans le fichier H5")
                dataset_path = self.h5_dataset
                print(f"🔑 Utilisation du dataset spécifié : '{dataset_path}'")
            else:
                dataset_path = self._select_best_h5_dataset(h5_file)
                print(f"🔑 Sélection automatique du dataset : '{dataset_path}'")
            
            # Charger les données
            dataset = h5_file[dataset_path]
            volume = np.array(dataset)
            print(f"📊 Volume chargé : {volume.shape}, type: {volume.dtype}")
            
            return volume
    
    def _print_h5_structure(self, group, prefix="", max_depth=3, current_depth=0):
        """Affiche la structure du fichier H5"""
        if current_depth >= max_depth:
            return
            
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Group):
                print(f"{prefix}📁 Groupe: {key}/")
                self._print_h5_structure(item, prefix + "  ", max_depth, current_depth + 1)
            elif isinstance(item, h5py.Dataset):
                print(f"{prefix}📄 Dataset: {key} - Shape: {item.shape}, Type: {item.dtype}")
    
    def _select_best_h5_dataset(self, h5_file):
        """Sélectionne automatiquement le meilleur dataset dans un fichier H5"""
        datasets = self._find_h5_datasets(h5_file)
        
        if not datasets:
            raise ValueError("Aucun dataset trouvé dans le fichier H5")
        
        candidates = []
        
        for dataset_path in datasets:
            dataset = h5_file[dataset_path]
            shape = dataset.shape
            dtype = dataset.dtype
            
            # Calculer un score pour ce dataset
            score = 0
            
            # Préférer les types numériques
            if np.issubdtype(dtype, np.integer) or np.issubdtype(dtype, np.floating):
                score += 100
            elif dtype == bool:
                score += 50
            
            # Préférer les données 3D
            if len(shape) == 3:
                score += 200
                # Bonus pour les dimensions raisonnables
                if all(dim > 1 for dim in shape):
                    score += 100
                # Préférer uint8 pour les masques/labels
                if dtype == np.uint8:
                    score += 50
            elif len(shape) == 2:
                score += 50
            elif len(shape) == 1:
                score += 1
            
            candidates.append((dataset_path, score, shape, dtype))
        
        # Trier par score décroissant
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        if candidates and candidates[0][1] > 0:
            selected_dataset = candidates[0][0]
            print(f"[AUTO] Sélection automatique : '{selected_dataset}' (score: {candidates[0][1]})")
            print(f"   Shape: {candidates[0][2]}, Type: {candidates[0][3]}")
            return selected_dataset
        else:
            raise ValueError("Aucun dataset approprié trouvé dans le fichier H5")
    
    def _find_h5_datasets(self, group, prefix=""):
        """Trouve tous les datasets dans un fichier H5"""
        datasets = []
        
        for key in group.keys():
            item = group[key]
            current_path = f"{prefix}/{key}" if prefix else key
            
            if isinstance(item, h5py.Dataset):
                datasets.append(current_path)
            elif isinstance(item, h5py.Group):
                datasets.extend(self._find_h5_datasets(item, current_path))
        
        return datasets
    
    def _save_nifti(self, volume):
        """Sauvegarde le volume au format NIfTI"""
        try:
            import nibabel as nib
        except ImportError:
            raise ImportError("nibabel n'est pas installé. Installez-le avec : pip install nibabel")
        
        # Assurer que le volume a au moins 3 dimensions
        if volume.ndim == 2:
            # Si c'est une image 2D, l'ajouter comme une seule slice
            volume = volume[np.newaxis, :, :]
            print(f"📊 Volume 2D converti en 3D : {volume.shape}")
        elif volume.ndim > 3:
            # Si plus de 3 dimensions, prendre les 3 premières
            print(f"⚠️  Volume avec {volume.ndim} dimensions détecté. Utilisation des 3 premières dimensions.")
            volume = volume[:, :, :] if volume.ndim == 4 else volume
        
        print(f"📊 Volume final : {volume.shape} (Z, Y, X)")
        print(f"📈 Valeurs : min={volume.min()}, max={volume.max()}, type={volume.dtype}")
        
        # Créer l'image NIfTI avec une matrice d'affine identité
        nifti_img = nib.Nifti1Image(volume, affine=np.eye(4))
        
        # Sauvegarder
        nib.save(nifti_img, self.output_path)
        print(f"💾 Volume sauvegardé au format NIfTI : {self.output_path}")


class BatchVolumeToNIfTIConverter:
    def __init__(self, input_folder, output_folder, npz_key=None, h5_dataset=None):
        """
        Convertit tous les volumes .npz ou .h5 d'un dossier en format NIfTI

        Args:
            input_folder: Dossier contenant les fichiers d'entrée (.npz ou .h5)
            output_folder: Dossier de sortie pour les fichiers .nii.gz
            npz_key: Clé spécifique pour fichiers NPZ (None pour sélection automatique)
            h5_dataset: Dataset spécifique pour fichiers H5 (None pour sélection automatique)
        """
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        self.npz_key = npz_key
        self.h5_dataset = h5_dataset

        # Vérifications
        if not self.input_folder.exists():
            raise FileNotFoundError(f"Le dossier d'entrée n'existe pas : {input_folder}")

        # Créer le dossier de sortie si nécessaire
        self.output_folder.mkdir(parents=True, exist_ok=True)

    def find_volume_files(self):
        """Trouve tous les fichiers .npz et .h5 dans le dossier d'entrée"""
        volume_files = []

        # Chercher les fichiers .npz
        npz_files = list(self.input_folder.glob("*.npz"))
        volume_files.extend(npz_files)

        # Chercher les fichiers .h5 et .hdf5
        h5_files = list(self.input_folder.glob("*.h5"))
        h5_files.extend(list(self.input_folder.glob("*.hdf5")))
        volume_files.extend(h5_files)

        return sorted(volume_files)

    def convert_batch(self):
        """Effectue la conversion en lot de tous les fichiers trouvés"""
        print(f"🚀 Conversion en lot de volumes vers NIfTI")
        print(f"📂 Dossier source : {self.input_folder}")
        print(f"📁 Dossier sortie : {self.output_folder}")

        # Trouver tous les fichiers de volume
        volume_files = self.find_volume_files()

        if not volume_files:
            print("❌ Aucun fichier .npz ou .h5 trouvé dans le dossier d'entrée")
            return False

        print(f"📊 {len(volume_files)} fichier(s) trouvé(s) à convertir")

        success_count = 0
        error_count = 0

        for i, input_file in enumerate(volume_files, 1):
            print(f"\n📄 [{i}/{len(volume_files)}] Traitement de : {input_file.name}")

            try:
                # Générer le nom de fichier de sortie
                output_name = input_file.stem + ".nii.gz"
                output_path = self.output_folder / output_name

                # Vérifier si le fichier de sortie existe déjà
                if output_path.exists():
                    print(f"⚠️  Fichier de sortie existe déjà, ignoré : {output_name}")
                    continue

                # Créer le convertisseur pour ce fichier
                converter = VolumeToNIfTIConverter(
                    input_path=input_file,
                    output_path=output_path,
                    npz_key=self.npz_key,
                    h5_dataset=self.h5_dataset
                )

                # Effectuer la conversion
                if converter.convert():
                    success_count += 1
                    print(f"✅ Conversion réussie : {output_name}")
                else:
                    error_count += 1
                    print(f"❌ Échec de la conversion : {input_file.name}")

            except Exception as e:
                error_count += 1
                print(f"❌ Erreur lors de la conversion de {input_file.name} : {str(e)}")

        print(f"\n📊 RÉSUMÉ DE LA CONVERSION EN LOT")
        print(f"✅ Conversions réussies : {success_count}")
        print(f"❌ Conversions échouées : {error_count}")
        print(f"📁 Total traité : {success_count + error_count}")

        return success_count > 0


def main():
    """Fonction principale"""
    print("🎯 CONVERSION VOLUME VERS NIFTI")
    print("=" * 50)

    # Déterminer le mode de fonctionnement
    if INPUT_FOLDER and INPUT_FOLDER.strip():
        # Mode dossier (traitement en lot)
        print("📁 MODE : Traitement en lot")
        print(f"📂 Dossier d'entrée : {INPUT_FOLDER}")
        print(f"📁 Dossier de sortie : {OUTPUT_FOLDER}")

        if NPZ_DATA_KEY:
            print(f"🔑 Clé NPZ spécifiée : {NPZ_DATA_KEY}")
        if H5_DATASET_PATH:
            print(f"🔑 Dataset H5 spécifié : {H5_DATASET_PATH}")

        print("=" * 50)

        try:
            # Créer le convertisseur en lot
            batch_converter = BatchVolumeToNIfTIConverter(
                input_folder=INPUT_FOLDER,
                output_folder=OUTPUT_FOLDER,
                npz_key=NPZ_DATA_KEY,
                h5_dataset=H5_DATASET_PATH
            )

            # Effectuer la conversion en lot
            success = batch_converter.convert_batch()

            if success:
                print("\n🎉 Conversion en lot terminée !")
            else:
                print("\n❌ Aucune conversion réussie")

        except Exception as e:
            print(f"\n❌ Erreur lors de la conversion en lot : {str(e)}")
            return False

    else:
        # Mode fichier unique
        print("📄 MODE : Fichier unique")
        print(f"📂 Fichier d'entrée : {INPUT_FILE}")
        print(f"📁 Fichier de sortie : {OUTPUT_FILE}")

        if NPZ_DATA_KEY:
            print(f"🔑 Clé NPZ spécifiée : {NPZ_DATA_KEY}")
        if H5_DATASET_PATH:
            print(f"🔑 Dataset H5 spécifié : {H5_DATASET_PATH}")

        print("=" * 50)

        try:
            # Créer le convertisseur
            converter = VolumeToNIfTIConverter(
                input_path=INPUT_FILE,
                output_path=OUTPUT_FILE,
                npz_key=NPZ_DATA_KEY,
                h5_dataset=H5_DATASET_PATH
            )

            # Effectuer la conversion
            success = converter.convert()

            if success:
                print("\n🎉 Conversion réussie !")
            else:
                print("\n❌ Échec de la conversion")

        except Exception as e:
            print(f"\n❌ Erreur lors de la conversion : {str(e)}")
            return False

    return True


if __name__ == "__main__":
    main()
