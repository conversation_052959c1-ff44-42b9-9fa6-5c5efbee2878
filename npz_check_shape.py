#!/usr/bin/env python3
"""
Script pour vérifier les dimensions et propriétés des fichiers NPZ
Similaire à nifti_check_shape.py mais pour les fichiers NPZ
"""

import numpy as np
import argparse
from pathlib import Path
import sys
import os

class NPZShapeChecker:
    def __init__(self, npz_path):
        """Initialise le vérificateur de forme NPZ"""
        self.npz_path = Path(npz_path)
        
        # Charger le fichier NPZ
        print(f"[LOAD] Chargement du fichier NPZ : {npz_path}")
        self.npz_data = np.load(npz_path)
        print(f"[INFO] Clés disponibles : {list(self.npz_data.keys())}")
        
        # Sélection automatique intelligente de la meilleure clé
        self.data_key = self._select_best_key()
        print(f"[KEY] Clé sélectionnée : '{self.data_key}'")
        
        # Charger les données du volume
        self.volume_raw = self.npz_data[self.data_key]
        
        # Analyser les propriétés
        self.analyze_properties()
    
    def _select_best_key(self):
        """Sélectionne automatiquement la meilleure clé de données à utiliser"""
        available_keys = list(self.npz_data.keys())
        
        # Sélection automatique
        if len(available_keys) == 1:
            return available_keys[0]
        
        # Plusieurs clés disponibles - sélection automatique intelligente
        print("Plusieurs clés disponibles. Sélection automatique de la meilleure clé :")
        
        # Analyser chaque clé pour trouver la meilleure
        candidates = []
        for key in available_keys:
            data = self.npz_data[key]
            shape = data.shape
            dtype = data.dtype
            
            print(f"  [FILE] '{key}' - Shape: {shape}, Type: {dtype}")
            
            # Critères de sélection (par ordre de priorité) :
            # 1. Doit être 3D (Z, Y, X)
            # 2. Préférer uint8
            # 3. Éviter les types booléens
            # 4. Éviter les tableaux 1D
            
            score = 0
            
            if len(shape) == 3:
                score += 100  # Priorité maximale pour 3D
                
                if dtype == 'uint8':
                    score += 50  # Bonus pour uint8
                elif dtype in ['uint16', 'int16', 'uint32', 'int32']:
                    score += 30  # Bonus moyen pour autres entiers
                elif dtype in ['float32', 'float64']:
                    score += 10  # Bonus faible pour float
                
                if dtype != 'bool':
                    score += 20  # Bonus pour non-booléen
                    
            elif len(shape) == 2:
                score += 50  # Acceptable pour 2D
            elif len(shape) == 1:
                score += 1   # Très faible priorité pour 1D
            
            candidates.append((key, score, shape, dtype))
        
        # Trier par score décroissant
        candidates.sort(key=lambda x: x[1], reverse=True)
        
        if candidates and candidates[0][1] > 0:
            selected_key = candidates[0][0]
            print(f"[BULLSEYE] Sélection automatique de la meilleure clé : '{selected_key}'")
            print(f"   Shape: {candidates[0][2]}, Type: {candidates[0][3]}")
            return selected_key
        else:
            print("[WARNING] Aucune clé appropriée trouvée (pas de données 3D)")
            raise ValueError("Aucune clé appropriée trouvée dans le fichier NPZ")
    
    def analyze_properties(self):
        """Analyse les propriétés du volume NPZ"""
        print(f"\n{'='*60}")
        print(f"[INFO] ANALYSE DES PROPRIÉTÉS")
        print(f"{'='*60}")
        
        # Informations de base
        print(f"[FILE] Fichier : {self.npz_path.name}")
        print(f"[LOAD] Chemin : {self.npz_path}")
        print(f"[SAVE] Taille fichier : {self._get_file_size()}")
        print(f"[KEY] Clé utilisée : {self.data_key}")
        
        # Propriétés du volume
        print(f"\n[INFO] PROPRIÉTÉS DU VOLUME :")
        print(f"   • Dimensions : {self.volume_raw.shape}")
        print(f"   • Nombre de dimensions : {self.volume_raw.ndim}")
        print(f"   • Type de données : {self.volume_raw.dtype}")
        print(f"   • Taille mémoire : {self.volume_raw.nbytes / (1024*1024):.2f} MB")
        print(f"   • Nombre total d'éléments : {self.volume_raw.size:,}")
        
        # Analyse des valeurs
        print(f"\n[DATA] ANALYSE DES VALEURS :")
        print(f"   • Valeur minimale : {self.volume_raw.min()}")
        print(f"   • Valeur maximale : {self.volume_raw.max()}")
        print(f"   • Valeur moyenne : {self.volume_raw.mean():.6f}")
        print(f"   • Écart-type : {self.volume_raw.std():.6f}")
        
        # Valeurs uniques (si pas trop nombreuses)
        unique_values = np.unique(self.volume_raw)
        if len(unique_values) <= 20:
            print(f"   • Valeurs uniques ({len(unique_values)}) : {unique_values}")
        else:
            print(f"   • Nombre de valeurs uniques : {len(unique_values)}")
            print(f"   • Premières valeurs : {unique_values[:10]}...")
        
        # Détection du type de données
        data_type = self._detect_data_type(unique_values)
        print(f"   • Type détecté : {data_type}")
        
        # Analyse par dimension
        self._analyze_dimensions()
        
        # Vérifications de compatibilité
        self._check_compatibility()
    
    def _get_file_size(self):
        """Retourne la taille du fichier en format lisible"""
        size_bytes = os.path.getsize(self.npz_path)
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024**2:
            return f"{size_bytes/1024:.1f} KB"
        elif size_bytes < 1024**3:
            return f"{size_bytes/(1024**2):.1f} MB"
        else:
            return f"{size_bytes/(1024**3):.1f} GB"
    
    def _detect_data_type(self, unique_values):
        """Détecte le type de données basé sur les valeurs uniques"""
        if len(unique_values) <= 5 and unique_values.max() <= 4 and unique_values.min() >= 0:
            if len(unique_values) == 2 and np.array_equal(unique_values, [0, 1]):
                return "Masque binaire"
            elif set(unique_values).issubset({0, 1, 2, 3, 4}):
                return "Masque de segmentation (5 classes)"
            else:
                return "Masque de segmentation (classes personnalisées)"
        elif unique_values.dtype in ['float32', 'float64'] and unique_values.max() <= 1.0:
            return "Volume d'intensité normalisé (0-1)"
        elif unique_values.dtype in ['uint8'] and unique_values.max() <= 255:
            return "Volume d'intensité (0-255)"
        else:
            return "Volume d'intensité (plage personnalisée)"
    
    def _analyze_dimensions(self):
        """Analyse détaillée des dimensions"""
        print(f"\n[SIZE] ANALYSE DES DIMENSIONS :")
        
        if self.volume_raw.ndim == 1:
            print(f"   • Volume 1D : {self.volume_raw.shape[0]} éléments")
        elif self.volume_raw.ndim == 2:
            print(f"   • Volume 2D : {self.volume_raw.shape[0]} x {self.volume_raw.shape[1]}")
            print(f"   • Hauteur : {self.volume_raw.shape[0]} pixels")
            print(f"   • Largeur : {self.volume_raw.shape[1]} pixels")
        elif self.volume_raw.ndim == 3:
            print(f"   • Volume 3D : {self.volume_raw.shape[0]} x {self.volume_raw.shape[1]} x {self.volume_raw.shape[2]}")
            print(f"   • Profondeur (Z) : {self.volume_raw.shape[0]} slices")
            print(f"   • Hauteur (Y) : {self.volume_raw.shape[1]} pixels")
            print(f"   • Largeur (X) : {self.volume_raw.shape[2]} pixels")
            
            # Analyse par slice pour les volumes 3D
            self._analyze_3d_slices()
        elif self.volume_raw.ndim == 4:
            print(f"   • Volume 4D : {' x '.join(map(str, self.volume_raw.shape))}")
            print(f"   • Dimension 0 : {self.volume_raw.shape[0]}")
            print(f"   • Dimension 1 : {self.volume_raw.shape[1]}")
            print(f"   • Dimension 2 : {self.volume_raw.shape[2]}")
            print(f"   • Dimension 3 : {self.volume_raw.shape[3]}")
        else:
            print(f"   • Volume {self.volume_raw.ndim}D : {' x '.join(map(str, self.volume_raw.shape))}")
    
    def _analyze_3d_slices(self):
        """Analyse spécifique pour les volumes 3D"""
        print(f"\n[INFO] ANALYSE DES SLICES 3D :")
        
        num_slices = self.volume_raw.shape[0]
        
        # Analyser quelques slices représentatives
        slice_indices = [0, num_slices//4, num_slices//2, 3*num_slices//4, num_slices-1]
        
        for i, slice_idx in enumerate(slice_indices):
            if slice_idx < num_slices:
                slice_data = self.volume_raw[slice_idx, :, :]
                unique_in_slice = len(np.unique(slice_data))
                non_zero = np.count_nonzero(slice_data)
                percentage_filled = (non_zero / slice_data.size) * 100
                
                print(f"   • Slice {slice_idx:3d} : {unique_in_slice:2d} valeurs uniques, {percentage_filled:5.1f}% non-zéro")
    
    def _check_compatibility(self):
        """Vérifie la compatibilité avec différents formats et outils"""
        print(f"\n[OK] VÉRIFICATIONS DE COMPATIBILITÉ :")
        
        # Vérifications générales
        if self.volume_raw.ndim >= 2:
            print(f"   [OK] Compatible avec les outils de visualisation 2D/3D")
        else:
            print(f"   [WARNING] Volume 1D - compatibilité limitée avec les outils de visualisation")
        
        if self.volume_raw.dtype in ['uint8', 'uint16', 'int16', 'float32']:
            print(f"   [OK] Type de données standard ({self.volume_raw.dtype})")
        else:
            print(f"   [WARNING] Type de données non standard ({self.volume_raw.dtype})")
        
        # Vérifications spécifiques
        if self.volume_raw.ndim == 3:
            print(f"   [OK] Compatible avec les exports PNG (slice par slice)")
            print(f"   [OK] Compatible avec la conversion vers NIfTI/H5")
        
        if self.volume_raw.size < 1e9:  # Moins de 1 milliard d'éléments
            print(f"   [OK] Taille raisonnable pour le traitement en mémoire")
        else:
            print(f"   [WARNING] Volume très volumineux - peut nécessiter un traitement par chunks")

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Vérificateur de forme pour fichiers NPZ")
    parser.add_argument("npz_file", help="Chemin vers le fichier NPZ")
    
    args = parser.parse_args()
    
    # Vérifier que le fichier existe
    if not Path(args.npz_file).exists():
        print(f"[ERROR] Erreur : Le fichier {args.npz_file} n'existe pas")
        sys.exit(1)
    
    try:
        # Créer et utiliser le vérificateur
        checker = NPZShapeChecker(args.npz_file)
        
        print(f"\n[OK] Analyse terminée pour {args.npz_file}")
        
    except Exception as e:
        print(f"[ERROR] Erreur lors de l'analyse : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
