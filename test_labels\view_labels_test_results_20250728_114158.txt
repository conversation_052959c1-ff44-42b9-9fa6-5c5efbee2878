Analyse des labels - 2025-07-28 11:41:58
Dossier analysé: test_labels
Nombre total de fichiers: 100
======================================================================

test_label_000000.png: [0, 1, 2, 3, 4]
test_label_000001.png: [0, 1, 2, 3, 4]
test_label_000002.png: [0, 1, 2, 3, 4]
test_label_000003.png: [0, 1, 2, 3, 4]
test_label_000004.png: [0, 1, 2, 3, 4]
test_label_000005.png: [0, 1, 2, 3, 4]
test_label_000006.png: [0, 1, 2, 3, 4]
test_label_000007.png: [0, 1, 2, 3, 4]
test_label_000008.png: [0, 1, 2, 3, 4]
test_label_000009.png: [0, 1, 2, 3, 4]
test_label_000010.png: [0, 1, 2, 3, 4]
test_label_000011.png: [0, 1, 2, 3, 4]
test_label_000012.png: [0, 1, 2, 3, 4]
test_label_000013.png: [0, 1, 2, 3, 4]
test_label_000014.png: [0, 1, 2, 3, 4]
test_label_000015.png: [0, 1, 2, 3, 4]
test_label_000016.png: [0, 1, 2, 3, 4]
test_label_000017.png: [0, 1, 2, 3, 4]
test_label_000018.png: [0, 1, 2, 3, 4]
test_label_000019.png: [0, 1, 2, 3, 4]
test_label_000020.png: [0, 1, 2, 3, 4]
test_label_000021.png: [0, 1, 2, 3, 4]
test_label_000022.png: [0, 1, 2, 3, 4]
test_label_000023.png: [0, 1, 2, 3, 4]
test_label_000024.png: [0, 1, 2, 3, 4]
test_label_000025.png: [0, 1, 2, 3, 4]
test_label_000026.png: [0, 1, 2, 3, 4]
test_label_000027.png: [0, 1, 2, 3, 4]
test_label_000028.png: [0, 1, 2, 3, 4]
test_label_000029.png: [0, 1, 2, 3, 4]
test_label_000030.png: [0, 1, 2, 3, 4]
test_label_000031.png: [0, 1, 2, 3, 4]
test_label_000032.png: [0, 1, 2, 3, 4]
test_label_000033.png: [0, 1, 2, 3, 4]
test_label_000034.png: [0, 1, 2, 3, 4]
test_label_000035.png: [0, 1, 2, 3, 4]
test_label_000036.png: [0, 1, 2, 3, 4]
test_label_000037.png: [0, 1, 2, 3, 4]
test_label_000038.png: [0, 1, 2, 3, 4]
test_label_000039.png: [0, 1, 2, 3, 4]
test_label_000040.png: [0, 1, 2, 3, 4]
test_label_000041.png: [0, 1, 2, 3, 4]
test_label_000042.png: [0, 1, 2, 3, 4]
test_label_000043.png: [0, 1, 2, 3, 4]
test_label_000044.png: [0, 1, 2, 3, 4]
test_label_000045.png: [0, 1, 2, 3, 4]
test_label_000046.png: [0, 1, 2, 3, 4]
test_label_000047.png: [0, 1, 2, 3, 4]
test_label_000048.png: [0, 1, 2, 3, 4]
test_label_000049.png: [0, 1, 2, 3, 4]
test_label_000050.png: [0, 1, 2, 3, 4]
test_label_000051.png: [0, 1, 2, 3, 4]
test_label_000052.png: [0, 1, 2, 3, 4]
test_label_000053.png: [0, 1, 2, 3, 4]
test_label_000054.png: [0, 1, 2, 3, 4]
test_label_000055.png: [0, 1, 2, 3, 4]
test_label_000056.png: [0, 1, 2, 3, 4]
test_label_000057.png: [0, 1, 2, 3, 4]
test_label_000058.png: [0, 1, 2, 3, 4]
test_label_000059.png: [0, 1, 2, 3, 4]
test_label_000060.png: [0, 1, 2, 3, 4]
test_label_000061.png: [0, 1, 2, 3, 4]
test_label_000062.png: [0, 1, 2, 3, 4]
test_label_000063.png: [0, 1, 2, 3, 4]
test_label_000064.png: [0, 1, 2, 3, 4]
test_label_000065.png: [0, 1, 2, 3, 4]
test_label_000066.png: [0, 1, 2, 3, 4]
test_label_000067.png: [0, 1, 2, 3, 4]
test_label_000068.png: [0, 1, 2, 3, 4]
test_label_000069.png: [0, 1, 2, 3, 4]
test_label_000070.png: [0, 1, 2, 3, 4]
test_label_000071.png: [0, 1, 2, 3, 4]
test_label_000072.png: [0, 1, 2, 3, 4]
test_label_000073.png: [0, 1, 2, 3, 4]
test_label_000074.png: [0, 1, 2, 3, 4]
test_label_000075.png: [0, 1, 2, 3, 4]
test_label_000076.png: [0, 1, 2, 3, 4]
test_label_000077.png: [0, 1, 2, 3, 4]
test_label_000078.png: [0, 1, 2, 3, 4]
test_label_000079.png: [0, 1, 2, 3, 4]
test_label_000080.png: [0, 1, 2, 3, 4]
test_label_000081.png: [0, 1, 2, 3, 4]
test_label_000082.png: [0, 1, 2, 3, 4]
test_label_000083.png: [0, 1, 2, 3, 4]
test_label_000084.png: [0, 1, 2, 3, 4]
test_label_000085.png: [0, 1, 2, 3, 4]
test_label_000086.png: [0, 1, 2, 3, 4]
test_label_000087.png: [0, 1, 2, 3, 4]
test_label_000088.png: [0, 1, 2, 3, 4]
test_label_000089.png: [0, 1, 2, 3, 4]
test_label_000090.png: [0, 1, 2, 3, 4]
test_label_000091.png: [0, 1, 2, 3, 4]
test_label_000092.png: [0, 1, 2, 3, 4]
test_label_000093.png: [0, 1, 2, 3, 4]
test_label_000094.png: [0, 1, 2, 3, 4]
test_label_000095.png: [0, 1, 2, 3, 4]
test_label_000096.png: [0, 1, 2, 3, 4]
test_label_000097.png: [0, 1, 2, 3, 4]
test_label_000098.png: [0, 1, 2, 3, 4]
test_label_000099.png: [0, 1, 2, 3, 4]
