# Core scientific computing
numpy>=1.21.0
matplotlib>=3.5.0
opencv-python>=4.5.0

# Medical imaging
nibabel>=3.2.0

# Image processing
Pillow>=8.3.0

# GUI frameworks
tkinter-dev>=0.1.0

# HDF5 file handling
h5py>=3.7.0

# File system operations
pathlib2>=2.3.0

# Data analysis
pandas>=1.3.0

# Progress bars (optional but useful)
tqdm>=4.62.0

# JSON handling (built-in, but for completeness)
# json - built-in module

# System operations (built-in)
# os, sys, shutil, argparse - built-in modules

# Date/time handling (built-in)
# datetime - built-in module

# Random operations (built-in)
# random - built-in module

# Regular expressions (built-in)
# re - built-in module

# Threading (built-in)
# threading - built-in module

# Collections (built-in)
# collections - built-in module