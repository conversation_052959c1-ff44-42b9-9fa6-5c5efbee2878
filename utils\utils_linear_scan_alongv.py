import h5py
import json
import os
import numpy as np
from PIL import Image, ImageDraw, ImageOps, ImageColor
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
from skimage.color import gray2rgb
from sklearn.cluster import KMeans
import copy

from .omniscan_div_cmap import get_omniscan_diverging_colormap

def safe_division(numerateur, denominateur):
    try:
        return numerateur/denominateur
    except ZeroDivisionError:
        return 0.0


# Function to list all keys in nde file
def print_allkeys(obj):
    "Recursively find all keys in an h5py.Group."
    keys = (obj.name,)
    if isinstance(obj, h5py.Group):
        for key, value in obj.items():
            if isinstance(value, h5py.Group):
                keys = keys + print_allkeys(value)
            else:
                keys = keys + (value.name,)
    return keys

def find_index_nearest_position(position, allpositions):
    difference_array = np.absolute(allpositions-position)
    if difference_array.min() <= 0.001:
        index = difference_array.argmin()
    else:
        index = float('nan')
    return index


def resize_img(img, xsz_px, ysz_px):
    
    return img.resize( (ysz_px, xsz_px) )


def rotate_img(img, angle_deg):
    
    return img.rotate( angle_deg )
    

def transform_data( files_dict, mask, transform_dict ):
    
    f_dict = copy.deepcopy(files_dict)
    trfm_dict = copy.deepcopy(transform_dict)

    # crop along v
    if 'crop_along_v' in trfm_dict:
        
        gr = f_dict["status_info"]["group"]
        
        if trfm_dict[ 'crop_along_v' ]['mode'] == 'auto':
            
            # selection rule for the data sequence
            # we don't keep first and last sequences of endviews without data; using kmeans algo
            pixels_sum_per_idx = np.sum(f_dict['data_array'], axis=(1,2))

            pixels_mean_per_idx = np.mean(f_dict['data_array']>0, axis=(1,2))
            threshold = np.mean(pixels_mean_per_idx[pixels_mean_per_idx > 0])
            idx_with_data_tmp = np.where(pixels_mean_per_idx > threshold )[0] 
            idx_min = idx_with_data_tmp[0]
            idx_max = idx_with_data_tmp[-1]

            idx_with_data = np.arange( idx_min, idx_max ) # keep all values between the first val > threshold and the last
            
            trfm_dict['crop_along_v']['pixels_sum_per_idx'] = pixels_sum_per_idx
            trfm_dict['crop_along_v']['pixels_mean_per_idx'] = pixels_mean_per_idx
            trfm_dict['crop_along_v']['threshold'] = threshold
            trfm_dict["crop_along_v"]["min_index"] = idx_min
            trfm_dict["crop_along_v"]["max_index"] = idx_max
            trfm_dict["crop_along_v"]["min_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[0]]
            trfm_dict["crop_along_v"]["max_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[-1]]
            
            # v-axis "Lengthwise" info update after the crop of the first sequences of endviews
            f_dict['status_info']['lengthwise']['offset'] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[0]]
            f_dict['status_info']['lengthwise']['quantity'] = idx_with_data.shape[0]

            
        else:

            if trfm_dict["crop_along_v"]["units"] == 'index':
                idx_with_data_tmp = np.zeros( (2,) )
                idx_with_data_tmp[0] = trfm_dict["crop_along_v"]["min"]
                idx_with_data_tmp[-1] = trfm_dict["crop_along_v"]["max"]
                idx_with_data = np.arange( idx_with_data_tmp[0], idx_with_data_tmp[-1] , dtype=np.uint64)
                
                trfm_dict["crop_along_v"]["min_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][trfm_dict["crop_along_v"]["min"]]
                trfm_dict["crop_along_v"]["max_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][trfm_dict["crop_along_v"]["max"]]
                
                trfm_dict["crop_along_v"]["min_index"] = trfm_dict["crop_along_v"]["min"]
                trfm_dict["crop_along_v"]["max_index"] = trfm_dict["crop_along_v"]["max"]
            elif trfm_dict["crop_along_v"]["units"] == 'data_units':
                
                idx_with_data_tmp = np.zeros( (2,) )
                idx_with_data_tmp[0] =  find_index_nearest_position(position = trfm_dict["crop_along_v"]["min"], allpositions = f_dict['status_info']['lengthwise']['positions_m'])
                idx_with_data_tmp[-1] = find_index_nearest_position(position = trfm_dict["crop_along_v"]["max"], allpositions = f_dict['status_info']['lengthwise']['positions_m'])
                idx_with_data = np.arange( idx_with_data_tmp[0], idx_with_data_tmp[-1] , dtype=np.uint64) 
                
                    
                trfm_dict["crop_along_v"]["min_index"] = idx_with_data[0]
                trfm_dict["crop_along_v"]["max_index"] = idx_with_data[-1]

                trfm_dict["crop_along_v"]["min_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[0]]
                trfm_dict["crop_along_v"]["max_data_units"] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[-1]]

         # v-axis "Lengthwise" info update after the crop of the first sequences of endviews
        f_dict['status_info']['lengthwise']['offset'] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data[0]]
        f_dict['status_info']['lengthwise']['quantity'] = idx_with_data.shape[0]        

        f_dict['status_info']['lengthwise']['positions_m'] = f_dict['status_info']['lengthwise']['positions_m'][idx_with_data]

        print("transform crop along v...")
        print("  start index / position [m]: " + str(idx_with_data[0]) + ' / ' + str(trfm_dict["crop_along_v"]["min_data_units"] ))
        print("  end index / position [m]:   " + str(idx_with_data[-1]) + ' / ' + str(trfm_dict["crop_along_v"]["max_data_units"] ))

        f_dict['data_array'] = f_dict['data_array'][idx_with_data,:,:]
        if isinstance(mask, (np.ndarray, np.generic)):
            mask = mask[idx_with_data,:,:]
       
        keys_to_delete = ['min', 'max', 'units']
        for key_to_del in keys_to_delete:
            if key_to_del in trfm_dict["crop_along_v"].keys():
                del trfm_dict["crop_along_v"][key_to_del]

    return f_dict, mask, trfm_dict
    
    


# Function to obtain a dictionary with infos that we need in all the nde_path in the list
def data_from_nde(list_of_nde_path, group_list = [], verbose=True):
    # Creation of a main dictionary of all nde_files infos that we need
    files_dict = {}

    for nde_file in list_of_nde_path:
        print(f"nde file : {nde_file}. (key : {nde_file[-11:-4]})")
        f = h5py.File(nde_file, 'r')
        
        # get and decode the json file about the configuration 
        # u = lengthwise axis, v = crosswise axis, into [m]
        json_str = f['Domain/Setup'][()]
        json_decoded = json.loads(json_str)
        
        NGroups = len( json_decoded["groups"] )
        
        data = {}
        
        
        # default to get all the groups
        if len(group_list) == 0:
            group_list = range(1, NGroups+1)
        
        
        for gr in group_list:
            data[gr] = {}
            
            if 'data' in json_decoded["groups"][gr-1].keys():
                data_str = 'data'
                
                if 'ascan' in json_decoded["groups"][gr-1][data_str]:
                
                    path = json_decoded["groups"][gr-1][data_str]['ascan']['dataset']['amplitude']['path']                
                    data[gr]['data_array'] = f[ path ][:]
                     
                    data[gr]['status_info'] = {}
                    data[gr]['status_info']['min_value'] = json_decoded["groups"][gr-1][data_str]['ascan']['dataset']['amplitude']['dataSampling']['min']
                    data[gr]['status_info']['max_value'] = json_decoded["groups"][gr-1][data_str]['ascan']['dataset']['amplitude']['dataSampling']['max']
                    data[gr]['status_info']['group_name'] = json_decoded["groups"][gr-1]['name']
                    data[gr]['status_info']['number_files_input'] = np.shape( data[gr]['data_array'] )[0]
                    data[gr]['status_info']['img_width_px'] = np.shape( data[gr]['data_array'] )[2]
                    data[gr]['status_info']['img_height_px'] = np.shape( data[gr]['data_array'] )[1]
                    
                    
                    data[gr]['status_info']['lengthwise'] = json_decoded['groups'][gr-1][data_str]['ascan']['dataset']['dimensions'][0]
                    data[gr]['status_info']['crosswise'] = json_decoded['groups'][gr-1][data_str]['ascan']['dataset']['dimensions'][1]
                    data[gr]['status_info']['ultrasound'] = json_decoded['groups'][gr-1][data_str]['ascan']['dataset']['dimensions'][2]
        
                    
                    # Conversion s (round-trip) -> meters for ultrasound infos 
                    ut_velocity = json_decoded['specimens'][0]['plateGeometry']['material']['longitudinalWave']['nominalVelocity']
                    data[gr]['status_info']['ultrasound']['resolution'] = data[gr]['status_info']['ultrasound']['resolution']*ut_velocity/2
                    data[gr]['status_info']['ultrasound']['offset'] = data[gr]['status_info']['ultrasound']['offset']*ut_velocity/2
        
                    # Static mapping of position/index
                    for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                        positions_m = []
                        for idx in range(data[gr]['status_info'][axis]['quantity']):
                            positions_m.append(data[gr]['status_info'][axis]['offset'] + idx * data[gr]['status_info'][axis]['resolution'])
                        data[gr]['status_info'][axis]['positions_m'] = np.array(positions_m)
        
                    if verbose:
                        print(f"Gr {gr}")
                        print(f"data_array : {data[gr]['data_array'].shape}. Min_value : {np.min(data[gr]['data_array'])}. Max_value : {np.max(data[gr]['data_array'])}. Mean : {np.mean(data[gr]['data_array'])}. Median : {np.median(data[gr]['data_array'])}")
                        nan_pourcentage = sum(sum(sum(np.isnan(data[gr]['data_array']))))/(data[gr]['data_array'].shape[0]*data[gr]['data_array'].shape[1]*data[gr]['data_array'].shape[2])
                        print(f"Pourcentage de données manquantes: {nan_pourcentage*100} %")
                        print("status_info :")
                        print(f"Lengthwise : {data[gr]['status_info']['lengthwise']['quantity']}")
                        for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                            print(f"{axis} : 'axis' : {data[gr]['status_info'][axis]['axis']},'quantity': {data[gr]['status_info'][axis]['quantity']}, 'resolution': {data[gr]['status_info'][axis]['resolution']}, 'offset': {data[gr]['status_info'][axis]['offset']}, '# positions_m': {data[gr]['status_info'][axis]['positions_m'].shape}")
    
                
            elif 'dataset' in json_decoded["groups"][gr-1].keys():
                data_str = 'dataset'
                
                if 'ascan' in json_decoded["groups"][gr-1][data_str]:
                    
                    path = json_decoded["groups"][gr-1][data_str]['ascan']['amplitude']['path']                
                    data[gr]['data_array'] = f[ path ][:]
                    
                    
                    data[gr]['status_info'] = {}
                    data[gr]['status_info']['min_value'] = json_decoded["groups"][gr-1][data_str]['ascan']['amplitude']['dataSampling']['min']
                    data[gr]['status_info']['max_value'] = json_decoded["groups"][gr-1][data_str]['ascan']['amplitude']['dataSampling']['max']
                    data[gr]['status_info']['group_name'] = json_decoded["groups"][gr-1]['name']
                    data[gr]['status_info']['number_files_input'] = np.shape( data[gr]['data_array'] )[0]
                    data[gr]['status_info']['img_width_px'] = np.shape( data[gr]['data_array'] )[2]
                    data[gr]['status_info']['img_height_px'] = np.shape( data[gr]['data_array'] )[1]
                    
                    
                    data[gr]['status_info']['lengthwise'] = json_decoded['groups'][gr-1][data_str]['ascan']['amplitude']['dimensions'][0]
                    data[gr]['status_info']['crosswise'] = json_decoded['groups'][gr-1][data_str]['ascan']['amplitude']['dimensions'][1]
                    data[gr]['status_info']['ultrasound'] = json_decoded['groups'][gr-1][data_str]['ascan']['amplitude']['dimensions'][2]
        
                    
                    # Conversion s (round-trip) -> meters for ultrasound infos 
                    # ut_velocity = json_decoded['specimens'][0]['plateGeometry']['material']['longitudinalWave']['nominalVelocity']
                    ut_velocity = json_decoded['groups'][gr-1][data_str]['ascan']['velocity']
                    data[gr]['status_info']['ultrasound']['resolution'] = data[gr]['status_info']['ultrasound']['resolution']*ut_velocity/2
                    data[gr]['status_info']['ultrasound']['offset'] = data[gr]['status_info']['ultrasound']['offset']*ut_velocity/2
        
                    # Static mapping of position/index
                    for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                        positions_m = []
                        for idx in range(data[gr]['status_info'][axis]['quantity']):
                            positions_m.append(data[gr]['status_info'][axis]['offset'] + idx * data[gr]['status_info'][axis]['resolution'])
                        data[gr]['status_info'][axis]['positions_m'] = np.array(positions_m)
        
                    if verbose:
                        print(f"Gr {gr}")
                        print(f"data_array : {data[gr]['data_array'].shape}. Min_value : {np.min(data[gr]['data_array'])}. Max_value : {np.max(data[gr]['data_array'])}. Mean : {np.mean(data[gr]['data_array'])}. Median : {np.median(data[gr]['data_array'])}")
                        nan_pourcentage = sum(sum(sum(np.isnan(data[gr]['data_array']))))/(data[gr]['data_array'].shape[0]*data[gr]['data_array'].shape[1]*data[gr]['data_array'].shape[2])
                        print(f"Pourcentage de données manquantes: {nan_pourcentage*100} %")
                        print("status_info :")
                        print(f"Lengthwise : {data[gr]['status_info']['lengthwise']['quantity']}")
                        for axis in ['lengthwise', 'crosswise', 'ultrasound']:
                            print(f"{axis} : 'axis' : {data[gr]['status_info'][axis]['axis']},'quantity': {data[gr]['status_info'][axis]['quantity']}, 'resolution': {data[gr]['status_info'][axis]['resolution']}, 'offset': {data[gr]['status_info'][axis]['offset']}, '# positions_m': {data[gr]['status_info'][axis]['positions_m'].shape}")

        # Keep data dictionary into main dictionary of all nde_files
        files_dict[f'{nde_file[-11:-4]}'] = data
        f.close()
        
    return files_dict

def get_status_array(nde_path, group_idx = 1):

    f = h5py.File(nde_path, 'r')

    json_str = f['Domain/Setup'][()]
    json_decoded = json.loads(json_str)

    status_path = json_decoded["groups"][np.max(group_idx-1, 0)]['dataset']['ascan']['status']['path']
    status_array = f[status_path][:]
    f.close()

    return status_array


# Function to generate endview "Dscan"
def gen_endview(runkey, gr, idx, files_dict, img_bits, colorize=True, show=True, printImg=False, output_path='./', printbb=False, bbs=[],
                path_omniscancolormap='./OmniScanColorMap.npy', transpose=False):
    '''
        bbs: List of dictionaries containing three keys, "label", "color" and "bb".  bb is a list of list. 
             One bounding box is a sequence of either [(x0, y0), (x1, y1)] or [x0, y0, x1, y1].
    '''
    
    if runkey:
        files_dict = files_dict[runkey][gr]

    img_data = files_dict['data_array'][idx,:,:].astype(np.int32)

    if transpose:
        img_data = img_data.T
        
    # retrieve min/max values in table and scale image
    min_value = files_dict['status_info']['min_value']
    max_value = files_dict['status_info']['max_value']
    img_data = safe_division(img_data-min_value, max_value-min_value)

    # Load colormap
    # Use the appropriate colormap
    if min_value < 0:
        # Omniscan Diverging colormap
        omniscan_cmap = get_omniscan_diverging_colormap(omniscan_cmap_path=path_omniscancolormap)
    else:
        OmniScanColorMap = np.load(path_omniscancolormap)
        omniscan_cmap = ListedColormap(OmniScanColorMap)
    
    # Colormap
    if colorize:
        img = Image.fromarray(np.uint8(omniscan_cmap(img_data)*int(2**img_bits-1) ))
    else:
        img = Image.fromarray(np.uint8(img_data*int(2**img_bits-1)), 'L')       # Mode L for 8-bit pixels, grayscale
            

    position = files_dict['status_info']['lengthwise']['positions_m'][idx]    # Position [in meters]
    
    if printbb:
        if not colorize:
            img = gray2rgb(img)
            img = Image.fromarray(img)
        img1 = ImageDraw.Draw(img)
        # img1.text((0,225), 'Position : '+"{:.3f}".format(position/1000)+' m', (0,0,0))
        for bb_info in bbs:
            for bb in bb_info["bb"]:
                # img1.rectangle(bb,outline='red')
                if transpose:
                    if isinstance(bb[0], tuple):
                        bb = [(bb[0][1], bb[0][0]), (bb[1][1], bb[1][0])]
                    else:
                        bb = [bb[1], bb[0], bb[3], bb[2]]
                img1.rectangle(bb, outline=bb_info["color"])
    
    if printImg:
        filename = output_path+'/endview_'+ str( int(position*1000*1000) ).zfill(12) + '.png'
        img.save(filename)
        
    if show:
        plt.figure(figsize=(15,10))
        plt.title(f'Gr {gr} - Endview {idx}. Position {position} meters ({position* 39.3701} inches)', color='white')
        plt.imshow(img)


        
# Function to generate topview "Cscan"
def gen_topview(files_dict, func, start_px, length_px):
    print('tbd')

    
# Function to generate sideview "Bscan"
def gen_sideview(files_dict, func):
    print('tbd')

#%%
if __name__ == "__main__":
    
    some_path = "C:/Users/<USER>/OneDrive - Olympus\Bureau/AI/Boeing - CFRP - AI/calibration data\L part/CMC-8276T-R-0.19-65.2DEG-2022-10-25-Run-001.nde"
    
    files_dict = data_from_nde([some_path])

  