#!/usr/bin/env python3
"""
Script de débogage pour comprendre pourquoi l'interface graphique
n'utilise pas le bon chemin pour view_mask.py
"""

import os
import sys
import re

def debug_view_mask_script():
    """Debug du script view_mask.py actuel"""
    print("🔍 Analyse du script view_mask.py actuel")
    print("=" * 50)
    
    if not os.path.exists("view_mask.py"):
        print("❌ view_mask.py n'existe pas!")
        return
    
    with open("view_mask.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Chercher toutes les définitions d'image_path
    patterns = [
        r'image_path\s*=\s*r?"[^"]*"',
        r"image_path\s*=\s*r?'[^']*'",
        r'visualize_image\([^)]*\)'
    ]
    
    print("📋 Contenu actuel du script:")
    lines = content.split('\n')
    for i, line in enumerate(lines, 1):
        if 'image_path' in line or 'visualize_image(' in line:
            print(f"  Ligne {i}: {line.strip()}")
    
    print("\n🔍 Patterns trouvés:")
    for i, pattern in enumerate(patterns, 1):
        matches = re.findall(pattern, content)
        print(f"  Pattern {i}: {pattern}")
        print(f"    Matches: {matches}")

def test_replacement_function():
    """Test de la fonction de remplacement"""
    print("\n🧪 Test de la fonction de remplacement")
    print("=" * 50)
    
    # Simuler la fonction de l'interface
    def _create_temp_visualize_mask_script(file_path):
        temp_script = "debug_temp_visualize_mask.py"
        with open("view_mask.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('→', '->')

        # Méthode plus robuste : remplacer toute ligne qui définit image_path
        import re
        # Échapper les backslashes dans le chemin pour éviter les erreurs regex
        escaped_file_path = file_path.replace('\\', '\\\\')
        
        # Pattern pour capturer n'importe quelle définition d'image_path
        pattern = r'image_path\s*=\s*r?"[^"]*"'
        replacement = f'image_path = r"{file_path}"'
        content = re.sub(pattern, replacement, content)
        
        # Si aucun remplacement n'a été fait, chercher d'autres patterns
        if file_path not in content:
            # Pattern alternatif pour les chemins avec des apostrophes
            pattern2 = r"image_path\s*=\s*r?'[^']*'"
            content = re.sub(pattern2, replacement, content)
            
            # Si toujours pas trouvé, chercher et remplacer l'appel à visualize_image
            if file_path not in content:
                # Chercher l'appel à visualize_image avec un paramètre
                pattern3 = r'(visualize_image\()[^)]*(\))'
                replacement3 = f'\\1r"{file_path}"\\2'
                content = re.sub(pattern3, replacement3, content)

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script, content
    
    # Test avec un chemin d'exemple
    test_path = "C:/Users/<USER>/Documents/test_image.png"
    
    try:
        temp_script, modified_content = _create_temp_visualize_mask_script(test_path)
        
        print(f"✅ Script temporaire créé: {temp_script}")
        
        if test_path in modified_content:
            print(f"✅ Chemin correctement remplacé: {test_path}")
        else:
            print(f"❌ Chemin NON remplacé!")
            print("Contenu modifié:")
            lines = modified_content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'image_path' in line or 'visualize_image(' in line:
                    print(f"  Ligne {i}: {line.strip()}")
        
        # Nettoyer
        if os.path.exists(temp_script):
            os.remove(temp_script)
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def check_interface_memory():
    """Vérifier les fichiers de mémoire de l'interface"""
    print("\n📁 Vérification des fichiers de configuration")
    print("=" * 50)
    
    config_files = [
        "interface_paths_memory.json",
        "last_paths_correspondants.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} existe")
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"   Contenu: {content[:200]}...")
        else:
            print(f"❌ {config_file} n'existe pas")

if __name__ == "__main__":
    print("🐛 Débogage de l'interface graphique - view_mask.py")
    print("=" * 60)
    
    debug_view_mask_script()
    test_replacement_function()
    check_interface_memory()
    
    print("\n" + "=" * 60)
    print("🔧 Recommandations:")
    print("1. Vérifiez que vous utilisez la bonne version de l'interface")
    print("2. Redémarrez complètement l'interface graphique")
    print("3. Sélectionnez un nouveau fichier via 'Parcourir'")
    print("4. Si le problème persiste, il pourrait y avoir un cache système")
