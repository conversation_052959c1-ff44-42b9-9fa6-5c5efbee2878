#!/usr/bin/env python3
import os
import sys
from pathlib import Path

def main(dataset_id):
    base_path = Path(os.environ.get("nnUNet_raw", "/mnt/Datasets/nnUnet/nnUnet_raw"))
    dataset_dirs = [d for d in base_path.iterdir() if d.is_dir() and d.name.startswith(f"Dataset{dataset_id}_")]
    if not dataset_dirs:
        sys.exit(f"❌ Dataset {dataset_id} introuvable dans {base_path}")
    if len(dataset_dirs) > 1:
        sys.exit(f"❌ Plusieurs datasets trouvés : {[d.name for d in dataset_dirs]}")

    dataset_dir = dataset_dirs[0]
    images_dir = dataset_dir / "imagesTr"
    labels_dir = dataset_dir / "labelsTr"

    if not images_dir.exists() or not labels_dir.exists():
        sys.exit("❌ Dossiers imagesTr ou labelsTr introuvables.")

    images = sorted([f for f in os.listdir(images_dir) if f.lower().endswith(".png")])
    labels = sorted([f for f in os.listdir(labels_dir) if f.lower().endswith(".png")])

    if not images or not labels:
        sys.exit("❌ Aucun fichier PNG trouvé.")
    if len(images) != len(labels) or any(i != l for i, l in zip(images, labels)):
        sys.exit("❌ Les fichiers ne correspondent pas 1-à-1 (même nom + extension).")

    print(f"🔄 Renommage de {len(images)} paires de fichiers...")
    for idx, (img, lbl) in enumerate(zip(images, labels), 1):
        num = f"{idx:04d}"
        img_ext = Path(img).suffix
        lbl_ext = Path(lbl).suffix

        os.rename(images_dir / img, images_dir / f"{num}_0000{img_ext}")
        os.rename(labels_dir / lbl, labels_dir / f"{num}{lbl_ext}")

    print("✅ Renommage terminé avec succès.")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        sys.exit(f"Usage: {sys.argv[0]} <dataset_id>")
    main(sys.argv[1])
