#!/usr/bin/env python3
"""
Script pour faire pivoter les images (PNG, JPG, etc.) de 90°, 180° ou 270°
Les images sont modifiées directement dans leur dossier d'origine.
"""
import os
from PIL import Image
import argparse
import sys
from pathlib import Path
import time

# === Dossier d'entrée à modifier ici ===
INPUT_FOLDER = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\images_to_rotate"  # <-- À modifier

# === Angle de rotation par défaut ===
DEFAULT_ROTATION = 90  # 90, 180 ou 270 degrés


def rotate_images_in_folder(folder_path, rotation_angle):
    """
    Fait pivoter toutes les images d'un dossier
    
    Args:
        folder_path (str): Chemin vers le dossier contenant les images
        rotation_angle (int): Angle de rotation (90, 180, 270)
    """
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise FileNotFoundError(f"Le dossier {folder_path} n'existe pas ou n'est pas un dossier.")
    
    # Extensions d'images supportées
    supported_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.gif'}
    
    # Trouver toutes les images dans le dossier (éviter les doublons avec un set)
    image_files_set = set()
    for ext in supported_extensions:
        image_files_set.update(folder.glob(f'*{ext}'))
        image_files_set.update(folder.glob(f'*{ext.upper()}'))

    # Convertir en liste triée
    image_files = sorted(list(image_files_set))
    
    if not image_files:
        print(f"Aucune image trouvée dans {folder_path}")
        return
    
    print(f"Trouve {len(image_files)} image(s) a faire pivoter de {rotation_angle} degres")
    
    success_count = 0
    error_count = 0
    
    for image_file in image_files:
        try:
            # Ouvrir l'image
            with Image.open(image_file) as img:
                # Faire la rotation
                # PIL utilise une rotation dans le sens anti-horaire
                # Pour une rotation horaire, on utilise l'angle négatif
                rotated_img = img.rotate(-rotation_angle, expand=True)
                
                # Sauvegarder l'image pivotée (écrase l'originale)
                rotated_img.save(image_file)
                
            print(f"[OK] {image_file.name} - pivote de {rotation_angle} degres")
            success_count += 1

        except Exception as e:
            print(f"[ERREUR] Erreur avec {image_file.name}: {str(e)}")
            error_count += 1

    print(f"\n[RESUME]")
    print(f"[SUCCES] Images pivotees avec succes: {success_count}")
    if error_count > 0:
        print(f"[ERREUR] Erreurs: {error_count}")
    print(f"[ROTATION] Rotation appliquee: {rotation_angle} degres")


def main():
    """Fonction principale"""
    # Protection contre les exécutions multiples
    lock_file = os.path.join(os.path.dirname(__file__), ".rotate_images_lock")

    if os.path.exists(lock_file):
        print("[AVERTISSEMENT] Une autre instance du script est deja en cours d'execution.")
        print("Si ce n'est pas le cas, supprimez le fichier:", lock_file)
        sys.exit(1)

    try:
        # Créer le fichier de verrouillage
        with open(lock_file, 'w') as f:
            f.write(f"PID: {os.getpid()}\nTime: {time.time()}")

        parser = argparse.ArgumentParser(description="Fait pivoter les images d'un dossier.")
        parser.add_argument("folder", nargs='?', default=INPUT_FOLDER,
                            help="Dossier contenant les images à pivoter (défaut: valeur hardcodée)")
        parser.add_argument("--rotation", "-r", type=int, choices=[90, 180, 270],
                            default=DEFAULT_ROTATION,
                            help="Angle de rotation en degrés (90, 180, 270) - défaut: 90")

        args = parser.parse_args()

        print(f"[ROTATION] Demarrage - PID: {os.getpid()}")
        print(f"[DOSSIER] {args.folder}")
        print(f"[ANGLE] {args.rotation} degres")
        print(f"[HEURE] {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)

        # Validation de l'angle
        if args.rotation not in [90, 180, 270]:
            print("[ERREUR] L'angle de rotation doit etre 90, 180 ou 270 degres")
            sys.exit(1)

        rotate_images_in_folder(args.folder, args.rotation)
        print("[SUCCES] Rotation terminee!")

    except Exception as e:
        print(f"[ERREUR] {e}")
        sys.exit(1)
    finally:
        # Supprimer le fichier de verrouillage
        try:
            if os.path.exists(lock_file):
                os.remove(lock_file)
        except Exception:
            pass


if __name__ == "__main__":
    main()
